import request from '@/config/axios'

export interface BankVO {
  id: number
  gpId: number
  bankType: number
  bankCode: string
}

export interface BankPageReqVO extends PageParam {
  gpId?: number
}

export interface BankExcelReqVO {
  gpId?: number
}

// 查询村医银行卡号列表
export const getBankPageApi = async (params: BankPageReqVO) => {
  return await request.get({ url: '/gp/bank/page', params })
}

// 查询村医银行卡号详情
export const getBankApi = async (id: number) => {
  return await request.get({ url: '/gp/bank/get?id=' + id })
}

// 新增村医银行卡号
export const createBankApi = async (data: BankVO) => {
  return await request.post({ url: '/gp/bank/create', data })
}

// 修改村医银行卡号
export const updateBankApi = async (data: BankVO) => {
  return await request.put({ url: '/gp/bank/update', data })
}

// 删除村医银行卡号
export const deleteBankApi = async (id: number) => {
  return await request.delete({ url: '/gp/bank/delete?id=' + id })
}

// 导出村医银行卡号 Excel
export const exportBankApi = async (params: BankExcelReqVO) => {
  return await request.download({ url: '/gp/bank/export-excel', params })
}
