import request from '@/config/axios'

export interface MdtVO {
  id: number
  name: string
  illnessId: number
  patientId: number
  sickId: number
  gpId: number
  duration: number
  startTime: Date
  endTime: Date
}

export interface MdtPageReqVO extends PageParam {
  sickId?: number
  gpId?: number
  startTime?: Date[]
}

export interface MdtExcelReqVO {
  sickId?: number
  gpId?: number
  startTime?: Date[]
}

// 查询多学科会诊列表
export const getMdtPageApi = async (params: MdtPageReqVO) => {
  return await request.get({ url: '/doctor/mdt/page', params })
}

// 查询多学科会诊详情
export const getMdtApi = async (id: number) => {
  return await request.get({ url: '/doctor/mdt/get?id=' + id })
}

// 查询多学科会诊详情 V2版本
export const getMdtV2Api = async (id: number) => {
  return await request.get({ url: '/doctor/mdt/v2/get?id=' + id })
}

// 新增多学科会诊
export const createMdtApi = async (data: MdtVO) => {
  return await request.post({ url: '/doctor/mdt/create', data })
}

// 修改多学科会诊
export const updateMdtApi = async (data: MdtVO) => {
  return await request.put({ url: '/doctor/mdt/update', data })
}

// 删除多学科会诊
export const deleteMdtApi = async (id: number) => {
  return await request.delete({ url: '/doctor/mdt/delete?id=' + id })
}

// 导出多学科会诊 Excel
export const exportMdtApi = async (params: MdtExcelReqVO) => {
  return await request.download({ url: '/doctor/mdt/export-excel', params })
}

// 根据医生id查询多学科列表
export const pgaeMyMdtByDoctorId = async (doctorId: number, pageNo: number, pageSize: number) => {
  return await request.get({
    url: `/doctor/mdt/page-my-mdt-by-doctorid?doctorId=${doctorId}&pageNo=${pageNo}&pageSize=${pageSize}`
  })
}
