//用户注册统计数据对象
export type UserRegisterStatsDataReqVo = {
  //统计类型，1-天，2-月，3-年
  type: number
  //统计数量
  count: number
  //统计标识
  statsMark: number
  //统计时间戳
  statsTimeStamp: number
}

//问诊订单统计数据对象
export type VisitOrderStatsDataReqVo = {
  //统计类型，1-天，2-月，3-年
  type: number
  //统计数量
  count: number
  //统计标识
  statsMark: number
  //总订单数
  allCount: number
  //总订单数
  textCount: number
  //总订单数
  audioCount: number
  //总订单数
  videoCount: number
  //统计时间戳
  statsTimeStamp: number
}

//药品订单统计数据对象
export type DrugOrderStatsDataReqVo = {
  //统计类型，1-天，2-月，3-年
  type: number
  //统计数量
  count: number
  //统计标识
  statsMark: number
  //全部订单
  allCount:number
  //已支付订单
  payount:number
  //统计时间戳
  statsTimeStamp: number

}

//用户注册统计图表对象
export type UserRegisterStatsEChartData={
  dataName:string[],
  dataValue:number[]
}

//问诊订单统计图表对象
export type VisitOrderStatsEChartData={
  //数据名称
  dataName:string[],
  //所有订单数据值
  dataAllValue:number[],
  //所有图文问诊数据值
  dataTextValue:number[],
  //所有音频问诊数据值
  dataAudioValue:number[],
  //所有视频问诊数据值
  dataVideoValue:number[],
}

//药品订单统计图表对象
export type DrugOrderStatsEChartData={
  //数据名称
  dataName:string[],
  //所有订单数据值
  dataAllValue:number[],
  //所有支付订单数据值
  dataPayValue:number[],
}
