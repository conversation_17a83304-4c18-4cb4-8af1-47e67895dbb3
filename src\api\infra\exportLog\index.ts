import request from '@/config/axios'

export interface ExportLogVO {
  exportId: number
  fileUrl: string
  type: string
  total: number
  userId: number
  status: boolean
  finishTime: string
  creator: string
  createTime: string
  exportFrom: string
}

export interface ExportLogPageReqVO extends PageParam {
  type?: string
  userId?: number
  status?: boolean
  createTime?: Date[]
  exportFrom?: string
}

export interface ExportLogExportReqVO {
  type?: string
  userId?: number
  status?: boolean
  createTime?: Date[]
  exportFrom?: string
}

// 查询数据导出日志列表
export const getExportLogPageApi = (params: ExportLogPageReqVO) => {
  return request.get({ url: '/infra/export-log/page', params })
}

// 查询数据导出日志详情
export const getExportLogApi = (id: number) => {
  return request.get({ url: '/infra/export-log/get?id=' + id })
}

// 导出数据导出日志 Excel
export const exportExportLogApi = (params: ExportLogExportReqVO) => {
  return request.download({
    url: '/infra/export-log/export-excel',
    params
  })
}

// 下载导出文件
export const downloadExportFileApi = (fileUrl: string) => {
  return request.download({
    url: '/infra/export-log/download',
    params: { fileUrl }
  })
}
