import request from '@/config/axios'

export interface InfoVO {
  id: number
  missionType: number
  missionRule: number
  missionRole: number
  missionJobId: number
}

export interface InfoPageReqVO extends PageParam {
  missionType?: number
  missionRule?: number
  missionRole?: number
  missionJobId?: number
  createTime?: Date[]
}

export interface InfoExcelReqVO {
  missionType?: number
  missionRule?: number
  missionRole?: number
  missionJobId?: number
  createTime?: Date[]
}

export interface JonInfoVO{
  id?:number
  jobDesc?:string
  executorHandler?:string
}

// 查询任务配置列表
export const getInfoPageApi = async (params: InfoPageReqVO) => {
  return await request.get({ url: '/mission/info/page', params })
}

// 查询任务配置详情
export const getInfoApi = async (id: number) => {
  return await request.get({ url: '/mission/info/get?id=' + id })
}
// 查询任务执行详情
export const getJobListApi = async () => {
  return await request.get({ url: '/mission/info/find-all-job-list'})
}



// 新增任务配置
export const createInfoApi = async (data: InfoVO) => {
  return await request.post({ url: '/mission/info/create', data })
}

// 修改任务配置
export const updateInfoApi = async (data: InfoVO) => {
  return await request.put({ url: '/mission/info/update', data })
}

// 删除任务配置
export const deleteInfoApi = async (id: number) => {
  return await request.delete({ url: '/mission/info/delete?id=' + id })
}

// 导出任务配置 Excel
export const exportInfoApi = async (params: InfoExcelReqVO) => {
  return await request.download({ url: '/mission/info/export-excel', params })
}

export const triggerApi=async(data:InfoVO)=>{
  return await request.post({ url: '/mission/info/do-trigger', data })
}

export const startApi=async(data:InfoVO)=>{
  return await request.post({ url: '/mission/info/do-start', data })
}

export const stopApi=async(data:InfoVO)=>{
  return await request.post({ url: '/mission/info/do-stop', data })
}


