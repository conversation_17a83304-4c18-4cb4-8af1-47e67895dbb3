import request from '@/config/axios'

export interface UserVO {
  id: number
  name: string
  birthday: Date
  phone: string
  headImg: string
  hospitalId: number
  districtCode: number
  deviceCode: string | null
  beGoodAt: string
  about: string
  status: number
  isAuth: number
  sex: number
  idCard: string
}

export interface UserPageReqVO extends PageParam {
  account?: string
  name?: string
  phone?: string
  headImg?: string
  hospitalId?: number
  districtCode?: number
  deviceCode?: string
  beGoodAt?: string
  about?: string
  status?: number
  isAuth?: number
  createTime?: Date[]
  sex?: number
}

export interface UserExcelReqVO {
  account?: string
  name?: string
  phone?: string
  headImg?: string
  hospitalId?: number
  districtCode?: number
  deviceCode?: string
  beGoodAt?: string
  about?: string
  status?: number
  isAuth?: number
  createTime?: Date[]
  sex?: number
}

// 查询村医信息列表
export const getUserPageApi = async (params: UserPageReqVO) => {
  return await request.get({ url: '/gp/user/page', params })
}

// 查询村医信息详情
export const getUserApi = async (id: number) => {
  return await request.get({ url: '/gp/user/get?id=' + id })
}

// 新增村医信息
export const createUserApi = async (data: UserVO) => {
  return await request.post({ url: '/gp/user/create', data })
}

// 获取导入模板
export const getImportTemplateApi = async () => {
  return await request.download({ url: '/gp/user/get-import-template' })
}

// 修改村医信息
export const updateUserApi = async (data: UserVO) => {
  return await request.put({ url: '/gp/user/update', data })
}

// 删除村医信息
export const deleteUserApi = async (id: number) => {
  return await request.delete({ url: '/gp/user/delete?id=' + id })
}

// 导出村医信息 Excel
export const exportUserApi = async (params: UserExcelReqVO) => {
  return await request.download({ url: '/gp/user/export-excel', params })
}

// 根据用户id集合 获得信息
export const getUserByIds = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/gp/user/list?ids=' + ids })
}

// 重置村医密码
export const resetGpPasswordApi = async (data) => {
  return await request.put({ url: 'gp/user/modify-password', data })
}
