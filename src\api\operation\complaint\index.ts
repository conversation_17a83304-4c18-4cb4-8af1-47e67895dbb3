import request from '@/config/axios'

export interface ComplaintVO {
  id: number
  patientId: number
  objectId: number
  type: number
  content: string
  imgType: number
}

export interface ComplaintPageReqVO extends PageParam {
  type?: number
  createTime?: Date[]
}

export interface ComplaintExcelReqVO {
  type?: number
  createTime?: Date[]
}

// 查询举报投诉列表
export const getComplaintPageApi = async (params: ComplaintPageReqVO) => {
  return await request.get({ url: '/patient/complaint/page', params })
}

// 查询举报投诉详情
export const getComplaintApi = async (id: number) => {
  return await request.get({ url: '/patient/complaint/get?id=' + id })
}

// 新增举报投诉
export const createComplaintApi = async (data: ComplaintVO) => {
  return await request.post({ url: '/patient/complaint/create', data })
}

// 修改举报投诉
export const updateComplaintApi = async (data: ComplaintVO) => {
  return await request.put({ url: '/patient/complaint/update', data })
}

// 删除举报投诉
export const deleteComplaintApi = async (id: number) => {
  return await request.delete({ url: '/patient/complaint/delete?id=' + id })
}

// 导出举报投诉 Excel
export const exportComplaintApi = async (params: ComplaintExcelReqVO) => {
  return await request.download({ url: '/patient/complaint/export-excel', params })
}
