import request from '@/config/axios'

export interface ImgsUrlVO {
  id: number
  url: string
  sort: number
  type: number
  objectId: number
  imgActionType: number
  imgActionValue: string
}

export interface ImgsUrlPageReqVO extends PageParam {
  url?: string
  sort?: number
  createTime?: Date[]
  type?: number
  objectId?: number
  imgActionType?: number
  imgActionValue?: string
}

export interface ImgsUrlExcelReqVO {
  url?: string
  sort?: number
  createTime?: Date[]
  type?: number
  objectId?: number
  imgActionType?: number
  imgActionValue?: string
}

// 查询图片url列表
export const getImgsUrlPageApi = async (params: ImgsUrlPageReqVO) => {
  return await request.get({ url: '/operate/imgs-url/page', params })
}

// 查询图片url详情
export const getImgsUrlApi = async (id: number) => {
  return await request.get({ url: '/operate/imgs-url/get?id=' + id })
}

// 新增图片url
export const createImgsUrlApi = async (data: ImgsUrlVO) => {
  return await request.post({ url: '/operate/imgs-url/create', data })
}

// 修改图片url
export const updateImgsUrlApi = async (data: ImgsUrlVO) => {
  return await request.put({ url: '/operate/imgs-url/update', data })
}

// 删除图片url
export const deleteImgsUrlApi = async (id: number) => {
  return await request.delete({ url: '/operate/imgs-url/delete?id=' + id })
}

// 导出图片url Excel
export const exportImgsUrlApi = async (params: ImgsUrlExcelReqVO) => {
  return await request.download({ url: '/operate/imgs-url/export-excel', params })
}
