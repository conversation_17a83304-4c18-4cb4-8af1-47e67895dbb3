import request from '@/config/axios'

export interface AddressVO {
  id: number
  patientId: number
  provinceCode: number
  cityCode: number
  districtCode: number
  name: string
  phone: string
  address: string
  isDefault: boolean
}

export interface AddressPageReqVO extends PageParam {
  patientId?: number
}

export interface AddressExcelReqVO {
  patientId?: number
}

// 查询患者邮寄地址信息列表
export const getAddressPageApi = async (params: AddressPageReqVO) => {
  return await request.get({ url: '/patient/address/page', params })
}

// 查询患者邮寄地址信息详情
export const getAddressApi = async (id: number) => {
  return await request.get({ url: '/patient/address/get?id=' + id })
}

// 新增患者邮寄地址信息
export const createAddressApi = async (data: AddressVO) => {
  return await request.post({ url: '/patient/address/create', data })
}

// 修改患者邮寄地址信息
export const updateAddressApi = async (data: AddressVO) => {
  return await request.put({ url: '/patient/address/update', data })
}

// 删除患者邮寄地址信息
export const deleteAddressApi = async (id: number) => {
  return await request.delete({ url: '/patient/address/delete?id=' + id })
}

// 导出患者邮寄地址信息 Excel
export const exportAddressApi = async (params: AddressExcelReqVO) => {
  return await request.download({ url: '/patient/address/export-excel', params })
}
