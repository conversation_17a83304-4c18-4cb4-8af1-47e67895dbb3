<template>
  <ContentWrap>
    <!-- 列表 -->
    <vxe-grid ref="xGrid" v-bind="gridOptions" class="xtable-scrollbar">
      <template #toolbar_buttons>
        <XButton
          type="warning"
          preIcon="ep:download"
          :title="t('action.export')"
          v-hasPermi="['patient:health-check-sum:export']"
          @click="handleExport()"
        />
      </template>
      <template #actionbtns_default="{ row }">
        <!-- 操作：详情 -->
        <XTextButton
          preIcon="ep:view"
          title="查看报告"
          v-if="row.bizType === 9"
          v-hasPermi="['visit:examination:report']"
          @click="handleReport(row)"
        />
        <XTextButton
          preIcon="ep:view"
          title="患者档案"
          v-hasPermi="['visit:examination:sick-detail']"
          @click="handleSickDetail(row)"
        />
      </template>
      <template #gpAreaCode="{ row }">
        <el-tag v-if="!row.gpAreaCode" type="danger">不存在</el-tag>
        <el-tag v-else type="success"> {{ row.gpAreaCode }} </el-tag>
      </template>
    </vxe-grid>
  </ContentWrap>
  <!-- 弹窗 -->
  <XModal id="visitModel" :loading="modelLoading" v-model="modelVisible" :title="modelTitle" >
    <!-- 表单：添加/修改 -->
    <MenuTab
      v-if="actionType === 'sickDetail'"
      :sickId="detailSickId"
      :default-descriptions="{
        schema: userSick.allSchemas.detailSchema,
        data: detailData
      }"
    />

    <div v-if="modelTitle === '查看报告'">
      <el-table :data="detailData.medicalList" height="250">
        <el-table-column 
          v-for="column in detailData.columns"
          :key="column.key"
          :label="column.title"
          :prop="column.key"
          >
          <template #default="{row}">
            <template v-if="column.key === 'medicalName'">
              <el-link>
                {{ row.medicalName + ` (${row.medicalCode})` }}
              </el-link>
            </template>
            <template v-if="column.key === 'result'">
              <el-link v-if="[0, 3].includes(parseInt(row.medicalStatus))">
                {{ row.result }}
              </el-link>
              <el-link v-if="parseInt(row.medicalStatus) < 3" type="danger">{{ row.result }} ↓</el-link>
              <el-link v-if="parseInt(row.medicalStatus) > 3" type="danger">{{ row.result }} ↑</el-link>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <div class="chart">
        <div class="chart-item">
        <ChartBox :chartData="refChartData.data.chartDataWbc" />
        </div>
        <div class="chart-item">
          <ChartBox :chartData="refChartData.data.chartDataRbc" />
        </div>
        <div class="chart-item">
          <ChartBox :chartData="refChartData.data.chartDataPlt" />
        </div>
      </div>
    </div>
  </XModal>
</template>
<script setup lang="ts" name="Visit">
// 全局相关的 import
import { ref } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useVxeGrid } from '@/hooks/web/useVxeGrid'
import { VxeGridInstance } from 'vxe-table'
// 业务相关的 import
import { allSchemas } from './examination.data'
import { getHealthListApi, pageInit } from '.'
// @ts-ignore
import { MenuTab } from '@/views/user/userSick/components/index'
import * as userSick from '@/views/user/userSick/userSick.data'
import { getUserSickApi } from '@/api/user/userSick'
import ChartBox from "@/components/chart/index.vue"
import { exportHealthAsyncApi } from '@/api/visit/examination'

const { t } = useI18n() // 国际化

const message = useMessage()

const sss: any = ref({})

sss.value = allSchemas

// 列表相关的变量
const xGrid = ref<VxeGridInstance>() // 列表 Grid Ref
const { gridOptions, getSearchData } = useVxeGrid({
  allSchemas: allSchemas,
  getListApi: getHealthListApi,
})

// 弹窗相关的变量
const modelVisible = ref(false) // 是否显示弹出层
const modelTitle = ref('edit') // 弹出层标题
const modelLoading = ref(false) // 弹出层loading
const actionType = ref('') // 操作按钮的类型
const detailData = ref() // 详情 Ref
const detailSickId = ref() // 患者id

const refChartData = reactive(
  {
    data: {
      index: 1,
      chartDataWbc: {},
      chartDataRbc: {},
      chartDataPlt: {},
    }
  }
)

onMounted(() => {
  pageInit()
})

// 设置标题
const setDialogTile = (type: string) => {
  modelLoading.value = true
  modelTitle.value = t('action.' + type)
  actionType.value = type
  modelVisible.value = true
}

const handleExport = async () => {
  try {
    // 获取当前列表的搜索参数
    const searchParams = await getSearchData(xGrid)
    console.log('导出参数:', searchParams)

    // 调用导出接口，传入搜索参数
    const res = await exportHealthAsyncApi(searchParams)
    console.log('导出结果:', res)

    message.success('导出请求已提交，请稍后查看基础设施-导出列表')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

const handleReport = async(row: any) => {
  setDialogTile('查看报告')
  modelTitle.value = '查看报告'
  detailData.value = row.result
  detailData.value.columns = [
          {
            title: '序号',
            dataIndex: 'sort',
            key: 'sort',
          },
          {
            title: '指标',
            dataIndex: 'medicalName',
            key: 'medicalName',
          },
          {
            title: '检测值',
            dataIndex: 'result',
            key: 'result',
          },
          {
            title: '参考值',
            dataIndex: 'medicalReference',
            key: 'medicalReference',
          },
          {
            title: '单位',
            dataIndex: 'medicalUnits',
            key: 'medicalUnits',
          }
        ]
  let chartDataWbc: any = {
    xAxisData: [],
    yAxis: {
      name: 'WBC',
      nameTextStyle: {
          padding: [0, 0, 0, 30]    // 四个数字分别为上右下左与原位置距离
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: false // 不显示坐标轴刻度线
      },
      axisLabel: {
        show: false // 不显示坐标轴上的文字
      },
    },
    xAxis: {
      data: [],
      axisTick: {
        show: false // 不显示坐标轴刻度线
      },
      axisLabel: {
        show: false, // 不显示坐标轴上的文字
      },
    },
    series: [
      {
        type: "line",
        symbol: 'none',
        smooth: true,
        data: [],
        itemStyle: {
          normal: {
            lineStyle: {
              color: '#000' //改变折线颜色
            }
          }
        }
      },
    ]
  }
  let chartDataRbc = JSON.parse(JSON.stringify(chartDataWbc))
  chartDataRbc.yAxis.name = 'RBC'
  let chartDataPlt = JSON.parse(JSON.stringify(chartDataWbc))
  chartDataPlt.yAxis.name = 'PLT'
  detailData.value.wbcCurve.forEach((element: any) => {
    chartDataWbc.xAxis.data.push(element[0])
    chartDataWbc.series[0].data.push(element[1])
  })
  detailData.value.rbcCurve.forEach((element: any) => {
    chartDataRbc.xAxis.data.push(element[0])
    chartDataRbc.series[0].data.push(element[1])
  })
  detailData.value.pltCurve.forEach((element: any) => {
    chartDataPlt.xAxis.data.push(element[0])
    chartDataPlt.series[0].data.push(element[1])
  })
  refChartData.data.chartDataWbc = chartDataWbc
  refChartData.data.chartDataRbc = chartDataRbc
  refChartData.data.chartDataPlt = chartDataPlt
  modelLoading.value = false
}

const handleSickDetail = async(row: any) => {
  detailSickId.value = row.sickId
  const res = await getUserSickApi(detailSickId.value)
  detailData.value = res
  setDialogTile('sickDetail')
  modelTitle.value = '患者详情'
  modelLoading.value = false
}
</script>

<style lang="css" scoped>
.chart {
  display: flex;
  .chart-item {
    flex: 1;
    &:nth-child(2) {
      margin: 0 20px;
    }
  }
}
</style>
