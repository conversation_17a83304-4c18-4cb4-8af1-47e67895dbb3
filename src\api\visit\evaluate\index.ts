import request from '@/config/axios'

export interface EvaluateVO {
  id: number
  level: number
  content: string
  doctorId: number
  patientId: number
  issues: string
  serviceType: number
}

export interface EvaluatePageReqVO extends PageParam {
  doctorId?: number
  patientId?: number
}

export interface EvaluateExcelReqVO {
  doctorId?: number
  patientId?: number
}

// 查询医生评价列表
export const getEvaluatePageApi = async (params: EvaluatePageReqVO) => {
  return await request.get({ url: '/doctor/evaluate/page', params })
}

// 查询医生评价详情
export const getEvaluateApi = async (id: number) => {
  return await request.get({ url: '/doctor/evaluate/get?id=' + id })
}

// 新增医生评价
export const createEvaluateApi = async (data: EvaluateVO) => {
  return await request.post({ url: '/doctor/evaluate/create', data })
}

// 修改医生评价
export const updateEvaluateApi = async (data: EvaluateVO) => {
  return await request.put({ url: '/doctor/evaluate/update', data })
}

// 删除医生评价
export const deleteEvaluateApi = async (id: number) => {
  return await request.delete({ url: '/doctor/evaluate/delete?id=' + id })
}

// 导出医生评价 Excel
export const exportEvaluateApi = async (params: EvaluateExcelReqVO) => {
  return await request.download({ url: '/doctor/evaluate/export-excel', params })
}


// 获得医生评价列表
export const getEvaluateByVisitIdsApi = async (visitIds: Array<number>) => {
  return await request.get({ url: '/doctor/evaluate/find-evaluates-by-visit-ids?visitIds=' + visitIds })
}




