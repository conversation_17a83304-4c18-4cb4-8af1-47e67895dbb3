export type SickRecordVO = {
  id: number
  allergicHistory: string
  familyHistory: string
  sickId: number
  height: bigdecimal
  weight: bigdecimal
  bloodType: number
  rhBloodType: number
  previousHistory: string
  exposureHistory: string
  geneticHistory: string
  disabilitySituation: string
}

export type SickRecordPageReqVO = {
  sickId: number
  height: bigdecimal
  weight: bigdecimal
  bloodType: number
  rhBloodType: number
  previousHistory: string
  exposureHistory: string
  geneticHistory: string
  disabilitySituation: string
}

export type SickRecordExcelReqVO = {
  sickId: number
  height: bigdecimal
  weight: bigdecimal
  bloodType: number
  rhBloodType: number
  previousHistory: string
  exposureHistory: string
  geneticHistory: string
  disabilitySituation: string
}