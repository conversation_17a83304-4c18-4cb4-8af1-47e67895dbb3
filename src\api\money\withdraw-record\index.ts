import request from '@/config/axios'

export interface WithdrawRecordVO {
  id: number
  userObjId: number
  userObjType: string
  name: string
  phone: string
  withdrawPrice: number
  withdrawedPrice: number
  bankType: number
  bankAccount: string
  status: number
}

export interface WithdrawRecordPageReqVO extends PageParam {
  userObjId?: number
  userObjType?: string
  status?: number
  createTime?: Date[]
}

export interface WithdrawRecordExcelReqVO {
  userObjId?: number
  userObjType?: string
  status?: number
  createTime?: Date[]
}

// 查询提现记录列表
export const getWithdrawRecordPageApi = async (params: WithdrawRecordPageReqVO) => {
  return await request.get({ url: '/operate/withdraw-record/page', params })
}

// 查询提现记录详情
export const getWithdrawRecordApi = async (id: number) => {
  return await request.get({ url: '/operate/withdraw-record/get?id=' + id })
}

// 新增提现记录
export const createWithdrawRecordApi = async (data: WithdrawRecordVO) => {
  return await request.post({ url: '/operate/withdraw-record/create', data })
}

// 修改提现记录
export const updateWithdrawRecordApi = async (data: WithdrawRecordVO) => {
  return await request.put({ url: '/operate/withdraw-record/update', data })
}

// 删除提现记录
export const deleteWithdrawRecordApi = async (id: number) => {
  return await request.delete({ url: '/operate/withdraw-record/delete?id=' + id })
}

// 导出提现记录 Excel
export const exportWithdrawRecordApi = async (params: WithdrawRecordExcelReqVO) => {
  return await request.download({ url: '/operate/withdraw-record/export-excel', params })
}
