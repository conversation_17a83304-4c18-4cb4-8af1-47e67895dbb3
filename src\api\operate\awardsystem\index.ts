import request from '@/config/axios'

export interface AwardSystemVO {
  id: number
  type: number
  userType: number
  userIds: string
  rate: number
  missionId: number
  groupId: number
}

export interface AwardSystemPageReqVO extends PageParam {
  type?: number
  userType?: number
  userIds?: string
  rate?: number
  missionId?: number
  createTime?: Date[]
  groupId?: number
}

export interface AwardSystemExcelReqVO {
  type?: number
  userType?: number
  userIds?: string
  rate?: number
  missionId?: number
  createTime?: Date[]
  groupId?: number
}

// 查询任务系统分配子项列表
export const getAwardSystemPageApi = async (params: AwardSystemPageReqVO) => {
  return await request.get({ url: '/operate/award-system/page', params })
}

// 查询任务系统分配子项详情
export const getAwardSystemApi = async (id: number) => {
  return await request.get({ url: '/operate/award-system/get?id=' + id })
}

// 新增任务系统分配子项
export const createAwardSystemApi = async (data: AwardSystemVO) => {
  return await request.post({ url: '/operate/award-system/create', data })
}

// 修改任务系统分配子项
export const updateAwardSystemApi = async (data: AwardSystemVO) => {
  return await request.put({ url: '/operate/award-system/update', data })
}

// 删除任务系统分配子项
export const deleteAwardSystemApi = async (id: number) => {
  return await request.delete({ url: '/operate/award-system/delete?id=' + id })
}

// 导出任务系统分配子项 Excel
export const exportAwardSystemApi = async (params: AwardSystemExcelReqVO) => {
  return await request.download({ url: '/operate/award-system/export-excel', params })
}

export const getAwardSystemListByGroupIds = async (groupIds: number[], type: number) => {
  return await request.get({
    url: `/operate/award-system/list-by-group-ids?ids=${groupIds}&type=${type}`
  })
}
