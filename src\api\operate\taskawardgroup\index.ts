import request from '@/config/axios'
import { awardOnStateEnum } from '@/common/enum'
export interface TaskAwardGroupVO {
  id: number
  type: number
  taskCount?: number
  peopleCount?: number
  unitPrice?: number
  awardPrice?: number
  repeatCount?: number
  isRepeat?: number
  awardType?: number
  status?: number
  userCheckRepeat?: number
  healthCount?: number
  healthType?: string
  awardStartTime?: number
  awardEndTime?: number
  jobTime?: string
  enable?: number
  doctorUserIds?: number[]
  villageUserIds?: number[]
  pharmacistUserIds?: number[]
  doctor_name?: string[]
  village_name?: string[]
  pharmacist_name?: string[]
}

export interface TaskAwardGroupPageReqVO extends PageParam {
  type?: number
  taskCount?: number
  peopleCount?: number
  unitPrice?: number
  awardPrice?: number
  repeatCount?: number
  isRepeat?: number
  awardType?: number
  status?: number
  userCheckRepeat?: number
  healthCount?: number
  healthType?: string
  awardStartTime?: number[]
  awardEndTime?: number[]
  jobTime?: string
  enable?: number
  createTime?: Date[]
}

export interface TaskAwardGroupExcelReqVO {
  type?: number
  taskCount?: number
  peopleCount?: number
  unitPrice?: number
  awardPrice?: number
  repeatCount?: number
  isRepeat?: number
  awardType?: number
  status?: number
  userCheckRepeat?: number
  healthCount?: number
  healthType?: string
  awardStartTime?: number[]
  awardEndTime?: number[]
  jobTime?: string
  enable?: number
  createTime?: Date[]
}

export interface TaskStateVO {
  id: number
  state: awardOnStateEnum
  notifyTitle: string
  notifyContent: string
}


// 查询任务奖励分组列表
export const getTaskAwardGroupPageApi = async (params: TaskAwardGroupPageReqVO) => {
  return await request.get({ url: '/operate/task-award-group/page', params })
}

export const getTaskAwardGroupListApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/task-award-group/list?ids=' + ids })
}

// 查询任务奖励分组详情
export const getTaskAwardGroupApi = async (id: number) => {
  return await request.get({ url: '/operate/task-award-group/get?id=' + id })
}

// 新增任务奖励分组
export const createTaskAwardGroupApi = async (data: TaskAwardGroupVO) => {
  return await request.post({ url: '/operate/task-award-group/create', data })
}

// 修改任务奖励分组
export const updateTaskAwardGroupApi = async (data: TaskAwardGroupVO) => {
  return await request.put({ url: '/operate/task-award-group/update', data })
}

// 删除任务奖励分组
export const deleteTaskAwardGroupApi = async (id: number) => {
  return await request.delete({ url: '/operate/task-award-group/delete?id=' + id })
}

// 导出任务奖励分组 Excel
export const exportTaskAwardGroupApi = async (params: TaskAwardGroupExcelReqVO) => {
  return await request.download({ url: '/operate/task-award-group/export-excel', params })
}


//修改启用状态
export const TaskStateDrugProfitGroupApi = async (data: TaskStateVO) => {
  return await request.post({ url: '/operate/task-award-group/task-on-state', data })
}
