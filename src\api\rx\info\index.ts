import request from '@/config/axios'

export interface InfoVO {
  id: number
  name: string
  code?: string
  about: string
  cityCode: string
  address: string
  contact: string
  contactPhone: string
  logo: string
  businessLicenseUrl: string
  businessCertificateUrl: string
  status: number
}

export interface InfoPageReqVO extends PageParam {
  name?: string
  about?: string
  code?: string
  cityCode?: string
  address?: string
  contact?: string
  contactPhone?: string
  logo?: string
  businessLicenseUrl?: string
  businessCertificateUrl?: string
  status?: number
  createTime?: Date[]
}

export interface InfoExcelReqVO {
  name?: string
  about?: string
  code?: string
  cityCode?: string
  address?: string
  contact?: string
  contactPhone?: string
  logo?: string
  businessLicenseUrl?: string
  businessCertificateUrl?: string
  status?: number
  createTime?: Date[]
}

// 查询药店信息列表
export const getInfoPageApi = async (params: InfoPageReqVO) => {
  return await request.get({ url: '/rx/info/page', params })
}

// 查询药店信息详情
export const getInfoApi = async (id: number) => {
  return await request.get({ url: '/rx/info/get?id=' + id })
}

// 根据用户id集合 获得信息
export const getInfoList = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/rx/info/list?ids=' + ids })
}

// 根据用户id集合 获得信息
export const getAllInfoList = async () => {
  return await request.get({ url: '/rx/info/find-all-info-list' })
}

// 新增药店信息
export const createInfoApi = async (data: InfoVO) => {
  return await request.post({ url: '/rx/info/create', data })
}

// 修改药店信息
export const updateInfoApi = async (data: InfoVO) => {
  return await request.put({ url: '/rx/info/update', data })
}

// 删除药店信息
export const deleteInfoApi = async (id: number) => {
  return await request.delete({ url: '/rx/info/delete?id=' + id })
}

// 导出药店信息 Excel
export const exportInfoApi = async (params: InfoExcelReqVO) => {
  return await request.download({ url: '/rx/info/export-excel', params })
}

// 门店分页
export const shopPageApi = async (data: any) => {
  return await request.post({ url: '/operate/shop/page', data })
}

// 门店详情
export const shopGetApi = async (id: number) => {
  return request.get({ url: '/operate/shop/get', params: { id } })
}

// 更新门店联系人、联系电话
export const shopUpdateContactApi = async (data: { id: number, contact: string, contactPhone: string }) => {
  return request.put({ url: '/operate/shop/update', data })
} 

// 导出门店
export const shopExportExcelApi = async (params: InfoExcelReqVO) => {
  return await request.download({ url: '/operate/shop/export-excel', params })
}





