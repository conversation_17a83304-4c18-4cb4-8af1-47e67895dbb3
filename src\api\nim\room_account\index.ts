import request from '@/config/axios'

export interface RoomAccountVO {
  id: number
  accountId: number
  userId: number
  userType: number
  userUuid: string
  userRole: string
  roomId: number
  roomUuid: string
}

export interface RoomAccountPageReqVO extends PageParam {
  accountId?: number
  userId?: number
  userType?: number
  userUuid?: string
  userRole?: string
  roomId?: number
  roomUuid?: string
  createTime?: Date[]
}

export interface RoomAccountExcelReqVO {
  accountId?: number
  userId?: number
  userType?: number
  userUuid?: string
  userRole?: string
  roomId?: number
  roomUuid?: string
  createTime?: Date[]
}

// 查询房间和账号的关系，即一个房间内有哪些账号加入了列表
export const getRoomAccountPageApi = async (params: RoomAccountPageReqVO) => {
  return await request.get({ url: '/nim/room-account/page', params })
}

// 查询房间和账号的关系，即一个房间内有哪些账号加入了详情
export const getRoomAccountApi = async (id: number) => {
  return await request.get({ url: '/nim/room-account/get?id=' + id })
}

// 新增房间和账号的关系，即一个房间内有哪些账号加入了
export const createRoomAccountApi = async (data: RoomAccountVO) => {
  return await request.post({ url: '/nim/room-account/create', data })
}

// 修改房间和账号的关系，即一个房间内有哪些账号加入了
export const updateRoomAccountApi = async (data: RoomAccountVO) => {
  return await request.put({ url: '/nim/room-account/update', data })
}

// 删除房间和账号的关系，即一个房间内有哪些账号加入了
export const deleteRoomAccountApi = async (id: number) => {
  return await request.delete({ url: '/nim/room-account/delete?id=' + id })
}

// 导出房间和账号的关系，即一个房间内有哪些账号加入了 Excel
export const exportRoomAccountApi = async (params: RoomAccountExcelReqVO) => {
  return await request.download({ url: '/nim/room-account/export-excel', params })
}
