import request from '@/config/axios'

export interface PharmacyVO {
  id: number
  name: string
  subbranch: number
  provinceCode: string
  cityCode: string
  districtCode: string
  contact: string
  phone: string
  address: string
  about: string
  logo: string
  businessPermit: string
  businessLicense: string
  status: number
}

export interface PharmacyPageReqVO extends PageParam {
  name?: string
  provinceCode?: string
  cityCode?: string
  createTime?: Date[]
}

export interface PharmacyExcelReqVO {
  name?: string
  provinceCode?: string
  cityCode?: string
  createTime?: Date[]
}

// 查询药店信息列表
export const getPharmacyPageApi = async (params: PharmacyPageReqVO) => {
  return await request.get({ url: '/operate/pharmacy/page', params })
}

// 查询药店信息详情
export const getPharmacyApi = async (id: number) => {
  return await request.get({ url: '/operate/pharmacy/get?id=' + id })
}

// 新增药店信息
export const createPharmacyApi = async (data: PharmacyVO) => {
  return await request.post({ url: '/operate/pharmacy/create', data })
}

// 修改药店信息
export const updatePharmacyApi = async (data: PharmacyVO) => {
  return await request.put({ url: '/operate/pharmacy/update', data })
}

// 删除药店信息
export const deletePharmacyApi = async (id: number) => {
  return await request.delete({ url: '/operate/pharmacy/delete?id=' + id })
}

// 导出药店信息 Excel
export const exportPharmacyApi = async (params: PharmacyExcelReqVO) => {
  return await request.download({ url: '/operate/pharmacy/export-excel', params })
}
