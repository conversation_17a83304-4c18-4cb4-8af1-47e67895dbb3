import request from '@/config/axios'

export interface DiagnoseSuggestVO {
  id: number
  initialDiagnosis: string
  content: string
  patientId: number
  sickId: number
  doctorId: number
  objectId: number
}

export interface DiagnoseSuggestPageReqVO extends PageParam {
  patientId?: number
  sickId?: number
  doctorId?: number
  objectType?: number
}

export interface DiagnoseSuggestExcelReqVO {
  patientId?: number
  sickId?: number
  doctorId?: number
  objectType?: number
}

// 查询诊断建议列表
export const getDiagnoseSuggestPageApi = async (params: DiagnoseSuggestPageReqVO) => {
  return await request.get({ url: '/patient/diagnose-suggest/page', params })
}

// 查询诊断建议详情
export const getDiagnoseSuggestApi = async (id: number) => {
  return await request.get({ url: '/patient/diagnose-suggest/get?id=' + id })
}

// 新增诊断建议
export const createDiagnoseSuggestApi = async (data: DiagnoseSuggestVO) => {
  return await request.post({ url: '/patient/diagnose-suggest/create', data })
}

// 修改诊断建议
export const updateDiagnoseSuggestApi = async (data: DiagnoseSuggestVO) => {
  return await request.put({ url: '/patient/diagnose-suggest/update', data })
}

// 删除诊断建议
export const deleteDiagnoseSuggestApi = async (id: number) => {
  return await request.delete({ url: '/patient/diagnose-suggest/delete?id=' + id })
}

// 导出诊断建议 Excel
export const exportDiagnoseSuggestApi = async (params: DiagnoseSuggestExcelReqVO) => {
  return await request.download({ url: '/patient/diagnose-suggest/export-excel', params })
}

// 获得诊断建议列表
export const getDiagnoseSuggestListObjectidApi = async (objectType: number, objectId: number) => {
  return await request.get({
    url: `/patient/diagnose-suggest/list-objectid?objectType=${objectType}&objectId=${objectId}`
  })
}

// 获得诊断建议列表详情
export const getDiagnoseSuggestListDetailApi = async (ids) => {
  return await request.get({
    url: `/patient/diagnose-suggest/list-detail?ids=${ids}`
  })
}
