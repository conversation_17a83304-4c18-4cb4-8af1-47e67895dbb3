import request from '@/config/axios'

export interface IncomePayVO {
}

export interface IncomePayPageReqVO extends PageParam {
  createTime?: Date[]
  doctorId?: number
  type?: number
  objectType?: number
  objectId?: number
}

export interface IncomePayExcelReqVO {
  createTime?: Date[]
  doctorId?: number
  type?: number
  objectType?: number
  objectId?: number
}

// 查询医生收支列表
export const getIncomePayPageApi = async (params: IncomePayPageReqVO) => {
  return await request.get({ url: '/doctor/income-pay/page', params })
}

// 查询医生收支详情
export const getIncomePayApi = async (id: number) => {
  return await request.get({ url: '/doctor/income-pay/get?id=' + id })
}

// 新增医生收支
export const createIncomePayApi = async (data: IncomePayVO) => {
  return await request.post({ url: '/doctor/income-pay/create', data })
}

// 修改医生收支
export const updateIncomePayApi = async (data: IncomePayVO) => {
  return await request.put({ url: '/doctor/income-pay/update', data })
}

// 删除医生收支
export const deleteIncomePayApi = async (id: number) => {
  return await request.delete({ url: '/doctor/income-pay/delete?id=' + id })
}

// 导出医生收支 Excel
export const exportIncomePayApi = async (params: IncomePayExcelReqVO) => {
  return await request.download({ url: '/doctor/income-pay/export-excel', params })
}
