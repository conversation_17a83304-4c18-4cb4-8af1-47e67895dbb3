export type BloodGlucoseVO = {
  id: number
  gpId: number
  sickId: number
  referenceType: number
  glu: bigdecimal
  checkResult: number
  checkContent: string
  deviceCode: string
  measuringTime: string
}

export type BloodGlucosePageReqVO = {
  gpId: number
  sickId: number
  referenceType: number
  glu: bigdecimal
  checkResult: number
  checkContent: string
  deviceCode: string
  measuringTime: string
  createTime: string
}

export type BloodGlucoseExcelReqVO = {
  gpId: number
  sickId: number
  referenceType: number
  glu: bigdecimal
  checkResult: number
  checkContent: string
  deviceCode: string
  measuringTime: string
  createTime: string
}