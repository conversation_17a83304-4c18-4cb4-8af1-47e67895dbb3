import request from '@/config/axios'

export interface BloodUricacidVO {
  id: number
  gpId: number
  sickId: number
  referenceType: number
  glu: number
  checkResult: number
  checkContent: string
  deviceCode: string
  measuringTime: Date
}

export interface BloodUricacidPageReqVO extends PageParam {
  gpId?: number
  sickId?: number
  referenceType?: number
  glu?: number
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

export interface BloodUricacidExcelReqVO {
  gpId?: number
  sickId?: number
  referenceType?: number
  glu?: number
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

// 查询血糖健康检查结果列表
export const getBloodUricacidPageApi = async (params: BloodUricacidPageReqVO) => {
  return await request.get({ url: '/patient/blood-uricacid/page', params })
}

// 查询血糖健康检查结果详情
export const getBloodUricacidApi = async (id: number) => {
  return await request.get({ url: '/patient/blood-uricacid/get?id=' + id })
}

// 新增血糖健康检查结果
export const createBloodUricacidApi = async (data: BloodUricacidVO) => {
  return await request.post({ url: '/patient/blood-uricacid/create', data })
}

// 修改血糖健康检查结果
export const updateBloodUricacidApi = async (data: BloodUricacidVO) => {
  return await request.put({ url: '/patient/blood-uricacid/update', data })
}

// 删除血糖健康检查结果
export const deleteBloodUricacidApi = async (id: number) => {
  return await request.delete({ url: '/patient/blood-uricacid/delete?id=' + id })
}

// 导出血糖健康检查结果 Excel
export const exportBloodUricacidApi = async (params: BloodUricacidExcelReqVO) => {
  return await request.download({ url: '/patient/blood-uricacid/export-excel', params })
}
