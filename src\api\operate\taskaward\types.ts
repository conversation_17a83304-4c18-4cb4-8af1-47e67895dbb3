export type TaskAwardVO = {
  id: number
  type: number
  userType: number
  userId: number
  taskCount: number
  unitPrice: number
  awardPrice: number
  repeatCount: number
  isRepeat: number
  awardType: number
  awardStartTime: string
  awardEndTime: string
  jobTime: string
  missionId: number
  status: number
  enable: number
}

export type TaskAwardPageReqVO = {
  type: number
  userType: number
  userId: number
  taskCount: number
  unitPrice: number
  awardPrice: number
  repeatCount: number
  isRepeat: number
  awardType: number
  awardStartTime: string
  awardEndTime: string
  jobTime: string
  createTime: string
  missionId: number
  status: number
  enable: number
}

export type TaskAwardExcelReqVO = {
  type: number
  userType: number
  userId: number
  taskCount: number
  unitPrice: number
  awardPrice: number
  repeatCount: number
  isRepeat: number
  awardType: number
  awardStartTime: string
  awardEndTime: string
  jobTime: string
  createTime: string
  missionId: number
  status: number
  enable: number
}