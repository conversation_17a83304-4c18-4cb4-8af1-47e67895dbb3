// import HttpService, { ResponseData } from '../http';
import service from '../https'
import { SuccessModel, ErrorModel, bodyModel } from '@/common/model/resModel'
import {
  CreateAudioTransParams,
  GetMessageBySessionIdParams,
  TranTaskModel,
  getMsgBySessionIdReturnModel,
  getSessionAccountModel
} from '@/common/model/im/imModel'
import { requestStatus } from '@/common/enum'

//http://192.168.0.191:13511/

/** 根据sessionId 获取 房间数据
 * @param { string } sessionId sid
 */
export const getImRoomBySessionId = (sessionId: string) =>
  service
    .get<getSessionAccountModel[]>('/app-api/nim/session-account/list-session-id', {
      data: {
        sessionId
      }
    })
    .then((response) => {
      return response as SuccessModel<getSessionAccountModel[]>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })

/** 根据会话获得聊天消息记录
 * @param {object} params AppMessageSearchReqVO
 * @param {number} params.pageNo 页码，从 1 开始
 * @param {number} params.pageSize 每页条数，最大值为 100
 * @param {string} params.sessionId
 * @param {string} params.accid
 * @param {number} params.startTimestamp
 * @param {number} params.endTimestamp
 * @returns
 */
export const getMsgBySessionId = (obj: GetMessageBySessionIdParams) =>
  service
    .post<getMsgBySessionIdReturnModel>('/app-api/nim/message/page-by-session', {
      ...obj
    })
    .then((response) => {
      return response as bodyModel<getMsgBySessionIdReturnModel>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })

/** 获取快照聊天消息
 * @param {object} params AppMessageSearchReqVO
 * @param {number} params.pageNo 页码，从 1 开始
 * @param {number} params.pageSize 每页条数，最大值为 100
 * @param {string} params.sessionId
 * @param {string} params.accid
 * @param {number} params.startTimestamp
 * @param {number} params.endTimestamp
 * @returns
 */
export const getMsgBySnapshot = (obj: GetMessageBySessionIdParams) =>
  service
    .post<getMsgBySessionIdReturnModel>(
      '/app-api/nim/message-snapshot/page-by-session-from-cache',
      {
        ...obj
      }
    )
    .then((response) => {
      return response as bodyModel<getMsgBySessionIdReturnModel>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })

/** 根据accid 获取 token
 * @param {string} accid accid
 * @returns
 */
export const getImTokenByAccid = (accid: string) =>
  service
    .post<bodyModel<string>>('/app-api/nim/account/find-im-token', {
      accid,
      token: ''
    })
    .then((response) => {
      return response as SuccessModel<string>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })

/** 创建音频消息转文字翻译拓展 (网易im的音频转文字)
 * @param {string} msgClientId 客户端消息唯一标识id
 * @param {string} lang 语音转对应文字语言类型
 * @param {string} trans 翻译文字内容
 * @returns
 */
export const createAudioToText = (obj: CreateAudioTransParams) =>
  service
    .post<bodyModel<number>>('/app-api/nim/audio-trans/create', {
      ...obj
    })
    .then((response) => {
      return response as SuccessModel<number>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })

/**
 * 创建云端音频转文字任务（阿里云的音频转文字）
 */
export const createTranTask = (obj: TranTaskModel) =>
  service
    .post<bodyModel<string>>('/app-api/nim/audio-trans/create-trans-service-task', {
      ...obj
    })
    .then((response) => {
      return response as SuccessModel<string>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })

/**  云端音频转文字任务结果（阿里云的音频转文字）
 */
export const findTransTask = (obj: TranTaskModel) =>
  service
    .post<bodyModel<string>>('/app-api/nim/audio-trans/find-trans-service-task', {
      ...obj
    })
    .then((response) => {
      return response as SuccessModel<string>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })

/** 阿里云 音频转文字联合任务
 * @param { string } audioLink 音频文件地址
 * @return { bodyModel<string> }
 */
export const createAudioToTextAsAL = async (audioLink: string) => {
  return new Promise<bodyModel<string>>(async (resolve, reject) => {
    try {
      const obj: TranTaskModel = {
        fileLink: audioLink,
        taskId: ''
      }

      let times: any = null

      const taskIdObj = await createTranTask(obj)
      if (taskIdObj.reqCode === requestStatus.success) {
        obj.taskId = taskIdObj.data

        times = setInterval(async () => {
          const res = await findTransTask(obj)

          //有内容 成功
          if (res.bCode === '1001700032' && res.data && res.data !== '') {
            clearInterval(times)
            resolve(res)
          } else if (res.bCode === '1001700031') {
            // 任务失败
            clearInterval(times)
            reject(new ErrorModel({}))
          }
        }, 1000)
      } else {
        reject(new ErrorModel({}))
      }
    } catch (e) {
      reject(new ErrorModel({}))
    }
  })
}

let timeDifference: number | undefined = void 0
/** 获取服务器时间 */
export const getTime = () => {
  if (timeDifference !== undefined && timeDifference.toString() !== 'NaN') {
    const currentTime = Date.now()

    return new SuccessModel({ data: currentTime + timeDifference })
  }

  return service
    .get<bodyModel<number>>('/app-api/nim/system/get-time')
    .then((response) => {
      const currentTime = Date.now()
      timeDifference = <number>response?.data - currentTime

      return response as SuccessModel<number>
    })
    .catch(() => {
      return new ErrorModel({ data: Date.now() })
    })
}

//
