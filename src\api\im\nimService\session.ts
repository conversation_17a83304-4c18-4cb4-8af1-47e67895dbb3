import { ErrorModel, SuccessModel } from '@/common/model/resModel'

import service from '../https'

/** 聊天消息会话列表设置已读
 * @param { string } sessionId sid
 * @param { string } accid accid
 */
export const postChatMessageRead = (sessionId: string, accid: string) =>
  service
    .post<boolean>('/app-api/nim/session-list/chat-message-read', {
      sessionId,
      accid
    })
    .then((response) => {
      return response as SuccessModel<boolean>
    })
    .catch(() => {
      return new ErrorModel({ data: false })
    })
