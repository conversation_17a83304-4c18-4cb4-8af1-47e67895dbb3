import request from '@/config/axios'

export interface TelemedicineVO {
  id: number
  doctorId: number
  illnessId: number
  gpId: number
  patientId: number
  sickId: number
  duration: number
  isSuggest: number
  age: number
}

export interface TelemedicinePageReqVO extends PageParam {
  doctorId?: number
  gpId?: number
  sickId?: number
  createTime?: Date[]
}

export interface TelemedicineExcelReqVO {
  doctorId?: number
  gpId?: number
  sickId?: number
  createTime?: Date[]
}

// 查询医生会诊列表
export const getTelemedicinePageApi = async (params: TelemedicinePageReqVO) => {
  return await request.get({ url: '/doctor/telemedicine/page', params })
}

// 查询医生监管会诊列表
export const getTelemedicinePagSuperviseApi = async (params: TelemedicinePageReqVO) => {
  return await request.get({ url: '/doctor/telemedicine/page-supervise', params })
}

// 查询医生会诊详情
export const getTelemedicineApi = async (id: number) => {
  return await request.get({ url: '/doctor/telemedicine/get?id=' + id })
}

// 查询医生会诊详情 v2版本
export const getTelemedicineV2Api = async (id: number) => {
  return await request.get({ url: '/doctor/telemedicine/v2/get?id=' + id })
}

// 新增医生会诊
export const createTelemedicineApi = async (data: TelemedicineVO) => {
  return await request.post({ url: '/doctor/telemedicine/create', data })
}

// 审核医生会诊
export const auditTelemedicineApi = async (data) => {
  return await request.post({ url: '/doctor/telemedicine/audit', data })
}

// 修改医生会诊
export const updateTelemedicineApi = async (data: TelemedicineVO) => {
  return await request.put({ url: '/doctor/telemedicine/update', data })
}

// 对接-撤销对接
export const updateTelemedicineSuperviseApi = async (data) => {
  return await request.put({ url: '/doctor/telemedicine/update-supervise-status', data })
}

// 删除医生会诊
export const deleteTelemedicineApi = async (id: number) => {
  return await request.delete({ url: '/doctor/telemedicine/delete?id=' + id })
}

// 导出医生会诊 Excel
export const exportTelemedicineApi = async (params: TelemedicineExcelReqVO) => {
  return await request.download({ url: '/doctor/telemedicine/export-excel', params })
}

// 根据医生id查询列表
export const pageTelemedicineByDoctorId = async (
  doctorId: number,
  pageNo: number,
  pageSize: number
) => {
  return await request.get({
    url: `/doctor/telemedicine/page-my-telemedicine-by-doctorid?doctorId=${doctorId}&pageNo=${pageNo}&pageSize=${pageSize}`
  })
}

// 根据村医id查询列表
export const pageTelemedicineByGpId = async (
  gpId: number,
  pageNo: number,
  pageSize: number
) => {
  return await request.get({
    url: `/doctor/telemedicine/page-my-telemedicine-by-gpid?gpId=${gpId}&pageNo=${pageNo}&pageSize=${pageSize}`
  })
}

// 根据会诊ID获取会诊状态，开始结束时间
export const getTeleReceptionByIds = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/doctor/reception/find-tele-reception-by-tele-ids?ids=' + ids })
}

//多学科 根据会诊ID获取会诊状态，开始结束时间
export const getMdtReceptionByIds = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/doctor/reception/find-mdt-reception-by-tele-ids?ids=' + ids })
}
