import request from '@/config/axios'

export interface DrugVO {
  id: number
  barCode: string
  approvalNumber: string
  guidePrice: number
  indate: string
  adverseReaction: string
  attention: string
  imgUrl: string
  size: string
  dose: string
  company: string
  taboo: string
  specification: string
  type: number
  cwType: number
  prescriptionType: number
  drugModel: number
  feature: string
  standardCode: String
  urlList: any[]
  natureType: number
}

export interface DrugPageReqVO extends PageParam {
  barCode?: string
  name?: string
  specification?: string
  type?: number
  cwType?: number
  prescriptionType?: number
  drugModel?: number
  feature?: string
}

export interface DrugExcelReqVO {
  barCode?: string
  name?: string
  specification?: string
  type?: number
  cwType?: number
  prescriptionType?: number
  drugModel?: number
  feature?: string
}

// 查询药品信息列表
export const getDrugPageApi = async (params: DrugPageReqVO) => {
  return await request.get({ url: '/operate/drug/page', params })
}

// 查询药品信息详情
export const getDrugApi = async (id: number) => {
  return await request.get({ url: '/operate/drug/get?id=' + id })
}

// 新增药品信息
export const createDrugApi = async (data: DrugVO) => {
  return await request.post({ url: '/operate/drug/create', data })
}

// 修改药品信息
export const updateDrugApi = async (data: DrugVO) => {
  return await request.put({ url: '/operate/drug/update', data })
}

// 删除药品信息
export const deleteDrugApi = async (id: number) => {
  return await request.delete({ url: '/operate/drug/delete?id=' + id })
}

// 导出药品信息 Excel
export const exportDrugApi = async (params: DrugExcelReqVO) => {
  return await request.download({ url: '/operate/drug/export-excel', params })
}

// 根据药品id集合 获得信息
export const getDrugByIds = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/operate/drug/list?ids=' + ids })
}
