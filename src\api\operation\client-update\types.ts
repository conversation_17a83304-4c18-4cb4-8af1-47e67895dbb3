export type ClientUpdateVO = {
  id: number
  version: string
  versionNum: number
  updateType: number
  lastForceVersionNum: number
  updateContent: string
  md5: string
  updateUrl: string
  effectTime: string
  resourceNum: number
  project: number
  clientType: number
}

export type ClientUpdatePageReqVO = {
  version: string
  versionNum: number
  updateType: number
  lastForceVersionNum: number
  updateContent: string
  md5: string
  updateUrl: string
  effectTime: string
  createTime: string
  resourceNum: number
  project: number
  clientType: number
}

export type ClientUpdateExcelReqVO = {
  version: string
  versionNum: number
  updateType: number
  lastForceVersionNum: number
  updateContent: string
  md5: string
  updateUrl: string
  effectTime: string
  createTime: string
  resourceNum: number
  project: number
  clientType: number
}