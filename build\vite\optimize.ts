const include = [
  'qs',
  'url',
  'vue',
  'sass',
  'mitt',
  'axios',
  'pinia',
  'dayjs',
  'qrcode',
  'windicss',
  'vue-router',
  'vue-types',
  'vue-i18n',
  'xe-utils',
  'crypto-js',
  'cropperjs',
  'lodash-es',
  'nprogress',
  'animate.css',
  'vxe-table',
  'vxe-table/es/style',
  'vxe-table/lib/locale/lang/zh-CN',
  'vxe-table/lib/locale/lang/en-US',
  'web-storage-cache',
  '@iconify/iconify',
  '@vueuse/core',
  '@zxcvbn-ts/core',
  'echarts/core',
  'echarts/charts',
  'echarts/components',
  'echarts/renderers',
  'echarts-wordcloud',
  '@wangeditor/editor',
  '@wangeditor/editor-for-vue',
  'element-plus',
  'element-plus/es',
  'element-plus/es/locale/lang/zh-cn',
  'element-plus/es/locale/lang/en',
  'element-plus/es/components/backtop/style/index',
  'element-plus/es/components/form/style/index',
  'element-plus/es/components/radio-group/style/index',
  'element-plus/es/components/radio/style/index',
  'element-plus/es/components/checkbox/style/index',
  'element-plus/es/components/checkbox-group/style/index',
  'element-plus/es/components/switch/style/index',
  'element-plus/es/components/time-picker/style/index',
  'element-plus/es/components/date-picker/style/index',
  'element-plus/es/components/col/style/index',
  'element-plus/es/components/form-item/style/index',
  'element-plus/es/components/alert/style/index',
  'element-plus/es/components/breadcrumb/style/index',
  'element-plus/es/components/select/style/index',
  'element-plus/es/components/input/style/index',
  'element-plus/es/components/breadcrumb-item/style/index',
  'element-plus/es/components/tag/style/index',
  'element-plus/es/components/pagination/style/index',
  'element-plus/es/components/table/style/index',
  'element-plus/es/components/table-column/style/index',
  'element-plus/es/components/card/style/index',
  'element-plus/es/components/row/style/index',
  'element-plus/es/components/button/style/index',
  'element-plus/es/components/menu/style/index',
  'element-plus/es/components/sub-menu/style/index',
  'element-plus/es/components/menu-item/style/index',
  'element-plus/es/components/option/style/index',
  'element-plus/es/components/dropdown/style/index',
  'element-plus/es/components/dropdown-menu/style/index',
  'element-plus/es/components/dropdown-item/style/index',
  'element-plus/es/components/skeleton/style/index'
]

const exclude = ['@iconify/json']

export { include, exclude }
