import request from '@/config/axios'

// 获取派单规则
export const getDispatchRule = () => {
  return request.get({ url: '/infra/config/getDispatchRule' })
}

// 保存派单规则
export const saveDispatchRule = (data) => {
  return request.post({ url: '/infra/config/saveDispatchRule', data })
}

// 获取等级规则
export const getLevelRule = () => {
  return request.get({ url: '/infra/config/getLevelRule' })
}

// 保存等级规则
export const saveLevelRule = (data) => {
  return request.post({ url: '/infra/config/saveLevelRule', data })
}

// 获得医生会诊调度记录分页
export const getDispatchRecordPage = (params) => {
  return request.get({ url: '/doctor/telemedicine/dispatchRecordPage', params })
}
