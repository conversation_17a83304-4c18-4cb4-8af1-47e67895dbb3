import request from '@/config/axios'

export interface SickRelationVO {
  id: number
  gpId: number
  sickId: number
}

export interface SickRelationPageReqVO extends PageParam {
  gpId?: number
  sickId?: number
  createTime?: Date[]
}

export interface SickRelationExcelReqVO {
  gpId?: number
  sickId?: number
  createTime?: Date[]
}

// 查询村医患者绑定关系列表
export const getSickRelationPageApi = async (params: SickRelationPageReqVO) => {
  return await request.get({ url: '/gp/sick-relation/page', params })
}

// 查询村医患者绑定关系详情
export const getSickRelationApi = async (id: number) => {
  return await request.get({ url: '/gp/sick-relation/get?id=' + id })
}

// 新增村医患者绑定关系
export const createSickRelationApi = async (data: SickRelationVO) => {
  return await request.post({ url: '/gp/sick-relation/create', data })
}

// 修改村医患者绑定关系
export const updateSickRelationApi = async (data: SickRelationVO) => {
  return await request.put({ url: '/gp/sick-relation/update', data })
}

// 删除村医患者绑定关系
export const deleteSickRelationApi = async (id: number) => {
  return await request.delete({ url: '/gp/sick-relation/delete?id=' + id })
}

// 导出村医患者绑定关系 Excel
export const exportSickRelationApi = async (params: SickRelationExcelReqVO) => {
  return await request.download({ url: '/gp/sick-relation/export-excel', params })
}

/** 查询村医患者绑定关系列表
 * @param { number[] } gpids 村一集合
 */
export const getSickRelationListByGpIdApi = async (gpids: number[]) => {
  return await request.get({ url: `/gp/sick-relation/find-list-by-gp-id?gpids=${gpids}` })
}

