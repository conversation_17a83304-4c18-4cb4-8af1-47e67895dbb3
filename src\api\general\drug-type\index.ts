import request from '@/config/axios'

export interface DrugTypeVO {
  id: number
  name: string
  parentId: number
  about: string
}

export interface DrugTypePageReqVO extends PageParam {
  id: number
  name: string
  parentId: number
  about: string
  createTime?: Date[]
}

// export interface DrugTypeExcelReqVO {
// }

// 查询药品分类列表
export const getDrugTypePageApi = async (params: DrugTypePageReqVO) => {
  return await request.get({ url: '/operate/drug-type/page', params })
}

// 查询药品分类详情
export const getDrugTypeApi = async (id: number) => {
  return await request.get({ url: '/operate/drug-type/get?id=' + id })
}

// 新增药品分类
export const createDrugTypeApi = async (data: DrugTypeVO) => {
  return await request.post({ url: '/operate/drug-type/create', data })
}

// 修改药品分类
export const updateDrugTypeApi = async (data: DrugTypeVO) => {
  return await request.put({ url: '/operate/drug-type/update', data })
}

// 删除药品分类
export const deleteDrugTypeApi = async (id: number) => {
  return await request.delete({ url: '/operate/drug-type/delete?id=' + id })
}


// 查询药品分类所有数据
export const getDrugTypAllApi = async () => {
  const data = await request.get({ url: '/operate/drug-type/find-drug-type-list' })
  return data
}
  
// // 导出药品分类 Excel
// export const exportDrugTypeApi = async (params: DrugTypeExcelReqVO) => {
//   return await request.download({ url: '/operate/drug-type/export-excel', params })
// }
