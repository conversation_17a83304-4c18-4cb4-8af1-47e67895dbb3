import request from '@/config/axios'

export interface FeedbackVO {
}

export interface FeedbackPageReqVO extends PageParam {
  objectType?: number
  objectId?: number
}

export interface FeedbackExcelReqVO {
  objectType?: number
  objectId?: number
}

// 查询意见反馈列表
export const getFeedbackPageApi = async (params: FeedbackPageReqVO) => {
  return await request.get({ url: '/operate/feedback/page', params })
}

// 查询意见反馈详情
export const getFeedbackApi = async (id: number) => {
  return await request.get({ url: '/operate/feedback/get?id=' + id })
}

// 新增意见反馈
export const createFeedbackApi = async (data: FeedbackVO) => {
  return await request.post({ url: '/operate/feedback/create', data })
}

// 修改意见反馈
export const updateFeedbackApi = async (data: FeedbackVO) => {
  return await request.put({ url: '/operate/feedback/update', data })
}

// 删除意见反馈
export const deleteFeedbackApi = async (id: number) => {
  return await request.delete({ url: '/operate/feedback/delete?id=' + id })
}

// 导出意见反馈 Excel
export const exportFeedbackApi = async (params: FeedbackExcelReqVO) => {
  return await request.download({ url: '/operate/feedback/export-excel', params })
}
