<template>
  <div
    :class="[
      'bg-[var(--el-color-white)] dark:(bg-[var(--el-bg-color)] border-[var(--el-border-color)] border-1px) visit_box'
    ]"
    style="height: 800px"
  >
    <el-descriptions title="问诊信息" direction="vertical" :column="4" border>
      <el-descriptions-item label="问诊编号">{{ data.orderNum }}</el-descriptions-item>
      <el-descriptions-item label="提交时间">{{
        formatDate(data.createTime)
      }}</el-descriptions-item>
      <el-descriptions-item label="问诊金额">{{ data.price }}</el-descriptions-item>
      <el-descriptions-item label="支付方式">{{ payName }}</el-descriptions-item>
      <el-descriptions-item label="问诊状态">
        <DictTag type="patient_visit_status" :value="data.status + ''" />
      </el-descriptions-item>

      <el-descriptions-item label="支付时间">{{
       data.visitOrder?.payTime? formatDate(data.visitOrder.payTime):"--"
      }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="患者信息" direction="vertical" :column="3" border>
      <el-descriptions-item label="患者姓名">{{ data.sickName }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{ data.sickPhone }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ data.sickSex }}</el-descriptions-item>
      <el-descriptions-item label="年龄">{{ data.sickAge }}</el-descriptions-item>
      <el-descriptions-item label="出生日期">{{ sickBirthday }}</el-descriptions-item>
      <el-descriptions-item label="病情描述">{{ data.sickContent }}</el-descriptions-item>
      <el-descriptions-item label="检查资料">
        <div v-if="data.sickCheckDataImages && data.sickCheckDataImages?.length > 0">
          <div
            v-for="(image, index) in data.sickCheckDataImages"
            :key="index"
            style="width: 150px; display: inline-block"
          >
            <!-- <img :src="image.url" alt="检查资料图像"  /> -->
            <ElImage
              :src="image.url"
              style="width: 100px; height: 100px"
              :preview-src-list="[image.url]"
            />
          </div>
        </div>
        <div v-else> 暂无 </div>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="接诊医师" direction="vertical" :column="5" border>
      <el-descriptions-item label="头像">
        <ElImage :src="data.doctorHeadImg" style="width: 40px; height: 40px" />
      </el-descriptions-item>
      <el-descriptions-item label="医师姓名">{{ data.doctorName }}</el-descriptions-item>
      <el-descriptions-item label="职称">{{ data.visitOrder?.officeHolderName?data.visitOrder?.officeHolderName:"--" }}</el-descriptions-item>
      <el-descriptions-item label="医院">{{ data.doctorHospitalName }}</el-descriptions-item>
      <el-descriptions-item label="科室">{{ data.doctorHospitalDepart }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="问诊记录">
      <el-descriptions-item style="height: 500px">
        <div style="width: 70%; height: 500px; margin: auto">
          <Doctor :sid="data.visitSid" />
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="音视频记录" direction="vertical" :column="1" border>
      <el-descriptions-item label="音频">
        <a
          download
          :href="url"
          target="_blank"
          v-for="url in media.audio"
          :key="url"
          style="display: block; margin-bottom: 10px; color: rgba(59, 130, 246, 1)"
        >
          音频 - {{ ge_time_format(url.split('/')[url.split('/').length - 1].slice(0, -4), '1') }}
        </a>
      </el-descriptions-item>
      <el-descriptions-item label="视频">
        <a
          download
          :href="url"
          target="_blank"
          v-for="url in media.video"
          :key="url"
          style="display: block; margin-bottom: 10px; color: rgba(59, 130, 246, 1)"
        >
          视频 - {{ ge_time_format(url.split('/')[url.split('/').length - 1].slice(0, -4), '1') }}
        </a>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="处方" direction="vertical" :column="1" border >
      <!-- <el-descriptions-item label="处方ID">{{ rowData.departName }}</el-descriptions-item>
            <el-descriptions-item label="患者姓名">{{ data.prescriptionNum }}</el-descriptions-item> -->
      <el-descriptions-item label="处方列表">
        <el-table :data="data.prescriptions" border  style="width: 100%"> 
          <el-table-column prop="prescriptionNum" label="处方ID" width="180" />
          <el-table-column prop="sickName" label="患者姓名" width="180" />
          <el-table-column prop="medicalCertificate" label="诊断说明" />
          <el-table-column prop="diagnoseSuggestName" label="临床诊断" />
          <el-table-column prop="status" label="处方状态">
            <template #default="{ row }">
              <DictTag :type="DICT_TYPE.PATIENT_PRESCRIPTION_STATUS" :value="row?.status" />
            </template>
          </el-table-column>
          <el-table-column prop="orderNum" label="药品订单" />
        </el-table>
      </el-descriptions-item>
      <!-- {{JSON.stringify(data.prescriptions) }} -->
    </el-descriptions>

    <el-descriptions title="患者病历" direction="vertical" :column="1" border>
      <el-descriptions-item label="初步诊断">{{ data.emr?.initialDiagnosis?data.emr?.initialDiagnosis:"--" }}</el-descriptions-item>
      <el-descriptions-item label="主诉">{{ data.emr?.principleAction ?data.emr?.principleAction:"--"}}</el-descriptions-item>
      <el-descriptions-item label="现病史">{{ data.emr?.currentIllness?data.emr?.currentIllness:"--" }}</el-descriptions-item>
      <el-descriptions-item label="个人史">{{ data.emr?.personIllness?data.emr?.personIllness:"--" }}</el-descriptions-item>
      <el-descriptions-item label="处置建议">{{ data.emr?.content?data.emr?.content:"--" }}</el-descriptions-item>
      <el-descriptions-item label="检查资料">
        <div v-if="data.doctorCheckDataImages && data.doctorCheckDataImages?.length > 0">
          <div
            v-for="(image, index) in data.doctorCheckDataImages"
            :key="index"
            style="width: 150px; display: inline-block"
          >
            <ElImage
              :src="image.url"
              style="width: 100px; height: 100px"
              :preview-src-list="[image.url]"
            />
          </div>
        </div>
        <div v-else> -- </div>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="患者评价" direction="vertical" :column="3" border v-if="data.evaluate">
      <el-descriptions-item label="评价星级">
        <el-rate :model-value="data.evaluate.level" disabled />
      </el-descriptions-item>
      <el-descriptions-item label="评价">{{ data.evaluate.content }}</el-descriptions-item>
      <el-descriptions-item label="时间">{{
        formatDate(data.evaluate.createTime)
      }}</el-descriptions-item>
    </el-descriptions>
    <el-descriptions
      v-if="followVisitList.data && followVisitList.data.length > 0"
      title="随访记录"
      direction="vertical"
      :column="5"
      class="descriptionsHideLabel"
      border
    >
      <template v-for="item in followVisitList.data" :key="item">
        <el-descriptions-item label="随访时间">{{
          item.visitTime ?dayjs(item.visitTime).format('YYYY-MM-DD'):"--"
        }}</el-descriptions-item>
        <el-descriptions-item label="治疗效果">
          {{ item.therapeuticEffect }}
        </el-descriptions-item>
        <el-descriptions-item label="用药依从性">
          {{ item.compliance }}
        </el-descriptions-item>
        <el-descriptions-item label="用药不良反应">
          {{ item.adverseReaction }}
        </el-descriptions-item>
        <el-descriptions-item label="其他说明">
          {{ item.content }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
  </div>
</template>
<script setup lang="ts">
import { getMediaData } from './visitdetail'
import { DICT_TYPE, getDictOptions, getDictObj } from '@/utils/dict'
import { ge_time_format } from '@/utils/custom'
import dayjs from 'dayjs'
import { adverseReactionEnum, complianceEnum } from '@/common/enum'
import { getFollowVisitByIdTypeApi } from '@/api/visit/followvisit'
const props = defineProps({
  data: {
    type: Object as PropType<any>,
    default: () => ({})
  },
  rowData: {
    type: Object as PropType<any>,
    default: () => ({})
  }
})

const media: Ref<{
  audio: string[]
  video: string[]
}> = ref({
  audio: [],
  video: []
})
let followVisitList = reactive<any>({
      data:[]
  }
)
// console.log('prescriptions_' + JSON.stringify(props.data))

onMounted(async () => {
  media.value.audio = []
  media.value.video = []
  const medioData = await getMediaData(props.data.visitSid)
 
    //#region 随访数据
   followVisitList.data = await getFollowVisitByIdTypeApi(props.data.visitId, props.data.visitType)

  if (followVisitList.data && followVisitList.data.length > 0) {
    followVisitList.data.forEach((item) => {
      if (item.therapeuticEffect) {
        item.therapeuticEffect = getDictObj(DICT_TYPE.THERAPEUTIC_EFFECT, item.therapeuticEffect)?.label
      }
      switch (item.compliance) {
        case complianceEnum.regular:
          item.compliance = '规律'
          break
        case complianceEnum.intermittent:
          item.compliance = '间断'
          break
        case complianceEnum.nomedication:
          item.compliance = '不用药'
          break
      }              

      switch (item.adverseReaction) {
        case adverseReactionEnum.no:
          item.adverseReaction = '无'
          break
        case adverseReactionEnum.yes:
          item.adverseReaction = '有'
          break
      }
    })
  }

  followVisitList.data = followVisitList.data

  //#endregion
  media.value.audio = medioData.audio
  media.value.video = medioData.video
})

// data.visitSid

// 在你的组件类中定义 formatDate 函数
function formatDate(timestamp: number): string {
  const date = new Date(timestamp) // 不需要乘以1000，因为已经是毫秒级的时间戳
  const formattedDate = date.toLocaleString() // 使用默认的日期时间格式
  return formattedDate
}

const payType = getDictOptions(DICT_TYPE.ORDER_PAY_TYPE)
console.log('payType',payType)

const payName = computed(() => {
  if (!props.data?.visitOrder?.payType) {
    return ''
  }
  return payType.filter((item: any) => item.value == props.data.visitOrder.payType)[0]?.label || ''
})

const sickBirthday = ge_time_format(props.data.sickBirthday, '2')
</script>

<style scoped>


.el-descriptions {
  margin-top: 20px;
}
.descriptionsHideLabel{
  padding-bottom:20px;
}

.cell-item {
  display: flex;
  align-items: center;
}

.margin-top {
  margin-top: 20px;
}

.visit_box {
  & :deep(table) , :deep(.el-table__empty-block){
    width:100% !important;
    table-layout: auto !important;
  }
 
}

</style>

<style lang="scss" scoped></style>
