import request from '@/config/axios'

export interface ReportVO {
  id: number
  userType: number
  userId: number
  type: number
  objectId: number
  content: string
  status: number
}

export interface ReportObjectStatusVO {
  objectId: number
  reportType: number
  objectStatus: number
}

export interface ReportPageReqVO extends PageParam {
  userType?: number
  userId?: number
  type?: number
  objectId?: number
  content?: string
  status?: number
  createTime?: Date[]
}

export interface ReportExcelReqVO {
  userType?: number
  userId?: number
  type?: number
  objectId?: number
  content?: string
  status?: number
  createTime?: Date[]
}

// 查询举报列表
export const getReportPageApi = async (params: ReportPageReqVO) => {
  return await request.get({ url: '/operate/report/page', params })
}

// 查询举报详情
export const getReportApi = async (id: number) => {
  return await request.get({ url: '/operate/report/get?id=' + id })
}

// 新增举报
export const createReportApi = async (data: ReportVO) => {
  return await request.post({ url: '/operate/report/create', data })
}

// 修改举报
export const updateReportApi = async (data: ReportVO) => {
  return await request.put({ url: '/operate/report/update', data })
}

// 删除举报
export const deleteReportApi = async (id: number) => {
  return await request.delete({ url: '/operate/report/delete?id=' + id })
}

// 修改关联对象状态
export const updateObjectStatus = async (data: ReportObjectStatusVO) => {
  return await request.put({ url: '/operate/report/update-object-status', data })
}


// 导出举报 Excel
export const exportReportApi = async (params: ReportExcelReqVO) => {
  return await request.download({ url: '/operate/report/export-excel', params })
}
