import request from '@/config/axios'

export interface TypeInfoVO {
  id: number
  name: string
  code: string
}

export interface TypeInfoPageReqVO extends PageParam {
  name?: string
  code?: string
  createTime?: Date[]
}

export interface TypeInfoExcelReqVO {
  name?: string
  code?: string
  createTime?: Date[]
}

// 查询任务类型列表
export const getTypeInfoPageApi = async (params: TypeInfoPageReqVO) => {
  return await request.get({ url: '/mission/type-info/page', params })
}

// 获得所有可用的任务奖励类型
export const getTypeListApi=async (ids:Array<number>)=>{
  if(ids.length<=0) return []
  return await request.get({url:'/mission/type-info/list?ids='+ids})
}

// 获得所有可用的任务奖励类型
export const getAllTypeInfoListApi=async ()=>{
  return await request.get({url:'/mission/type-info/list-all'})
}


// 查询任务类型详情
export const getTypeInfoApi = async (id: number) => {
  return await request.get({ url: '/mission/type-info/get?id=' + id })
}

// 新增任务类型
export const createTypeInfoApi = async (data: TypeInfoVO) => {
  return await request.post({ url: '/mission/type-info/create', data })
}

// 修改任务类型
export const updateTypeInfoApi = async (data: TypeInfoVO) => {
  return await request.put({ url: '/mission/type-info/update', data })
}

// 删除任务类型
export const deleteTypeInfoApi = async (id: number) => {
  return await request.delete({ url: '/mission/type-info/delete?id=' + id })
}

// 导出任务类型 Excel
export const exportTypeInfoApi = async (params: TypeInfoExcelReqVO) => {
  return await request.download({ url: '/mission/type-info/export-excel', params })
}
