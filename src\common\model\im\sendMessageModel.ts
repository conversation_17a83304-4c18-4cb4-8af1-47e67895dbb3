// import { dataModel as textModel } from '@/components/im/components/singlechat/components/chatstyle/text/model';
// import { dataModel as audioModel } from '@/components/im/components/singlechat/components/chatstyle/audio/model';
import { dataModel as caseModel } from '@/components/im/components/singlechat/components/chatstyle/case/model';
import { dataModel as consultingModel } from '@/components/im/components/singlechat/components/chatstyle/consulting/model';
// import { dataModel as imageModel } from '@/components/im/components/singlechat/components/chatstyle/image/model';
import { dataModel as prescriptionModel } from '@/components/im/components/singlechat/components/chatstyle/prescription/model';
import { dataModel as recommendationModel } from '@/components/im/components/singlechat/components/chatstyle/recommendation/model';
import { dataModel as reservationModel } from '@/components/im/components/singlechat/components/chatstyle/reservation/model';
import { dataModel as serviceInstructionsModel } from '@/components/im/components/singlechat/components/chatstyle/serviceInstructions/model';
import { dataModel as systemModel } from '@/components/im/components/singlechat/components/chatstyle/system/model';
import { dataModel as timeModel } from '@/components/im/components/singlechat/components/chatstyle/time/model';
import { dataModel as videoModel } from '@/components/im/components/singlechat/components/chatstyle/text/model';
import { dataModel as visitModel } from '@/components/im/components/singlechat/components/chatstyle/visit/model';
import { dataModel as rtcStatusModel } from '@/components/im/components/singlechat/components/chatstyle/rtcstatus/model';

/** 消息类型枚举
 * @param { number } text 文本
 * @param { number } image 图片
 * @param { number } audio 音频
 * @param { number } video 视频
 * @param { number } system 系统消息
 * @param { number } case 病例
 * @param { number } prescription 处方
 * @param { number } reservation 预约单
 * @param { number } consulting 问诊
 * @param { number } time 时间
 * @param { number } recommendation 用药建议
 * @param { number } serviceInstructions 问诊服务须知
 * @param { number } visit 随访
 */
export enum customMsgEnum {
  text = 10001,
  image,
  audio,
  video,
  system,
  case,
  prescription,
  reservation,
  consulting,
  time,
  recommendation,
  serviceInstructions,
  visit,
  rtcSend,
  rtcStatus,
}

/** 消息类型对应的模型 */
// export type costomMsgEvents = {
//   [customMsgEnum.text]: textModel;
//   [customMsgEnum.image]: imageModel;
//   [customMsgEnum.audio]: audioModel;
//   [customMsgEnum.video]: videoModel;
//   [customMsgEnum.system]: systemModel;
//   [customMsgEnum.case]: caseModel;
//   [customMsgEnum.prescription]: prescriptionModel;
//   [customMsgEnum.reservation]: reservationModel;
//   [customMsgEnum.consulting]: consultingModel;
//   [customMsgEnum.time]: timeModel;
//   [customMsgEnum.recommendation]: recommendationModel;
// };

/** 消息类型对应的模型 */
export type sendCostomMsgEvents = {
  [customMsgEnum.text]: sendTextModel;
  [customMsgEnum.image]: sendImageModel;
  [customMsgEnum.audio]: sendAudioModel;
  [customMsgEnum.video]: videoModel;
  [customMsgEnum.system]: systemModel;
  [customMsgEnum.case]: caseModel;
  [customMsgEnum.prescription]: prescriptionModel;
  [customMsgEnum.reservation]: reservationModel;
  [customMsgEnum.consulting]: consultingModel;
  [customMsgEnum.time]: timeModel;
  [customMsgEnum.recommendation]: recommendationModel;
  [customMsgEnum.serviceInstructions]: serviceInstructionsModel;
  [customMsgEnum.visit]: visitModel;
  [customMsgEnum.rtcSend]: sendTextModel;
  [customMsgEnum.rtcStatus]: rtcStatusModel;
};

/** 发送文本 */
export type sendTextModel = {
  message: string;
};

/** 发送图片 */
export type sendImageModel = {
  blob: Blob;
  base64: string;
};

/** 发送音频 */
export type sendAudioModel = {
  blob: Blob;
  duration: number;
};

/** 处方状态枚举
 * @param { number } all 全部订单
 * @param { number } square 已开方(审核中)
 * @param { number } passed 审核通过(可使用)
 * @param { number } paid 支付完成(已使用)
 * @param { number } nopassed 未通过打回
 * @param { number } revocation 撤销
 * @param { number } overtime 超时(已失效)
 */
export enum prescriptionTypeEnum {
  all = 0,
  square,
  passed,
  paid,
  nopassed,
  revocation,
  overtime,
}

/** 问诊类型
 * @param { number } text 图文
 * @param { number } video 视频
 * @param { number } audio 音频
 */
export enum visitTypeEnum {
  text = 1,
  video,
  audio,
}

/** 性别枚举
 * @param { number } man 男人
 * @param { number } woman 女人
 */
export enum sexEnum {
  man = 1,
  woman,
}

/** 患者信息模型
 * @param { number } id 患者id
 * @param { string } name 患者姓名
 * @param { sexEnum } sex 性别
 * @param { number } age 年龄
 */
export type sickModel = {
  id: number;
  name: string;
  sex: sexEnum;
  age: number;
};

/** 药品模型
 * @param { string } name 药品名称
 * @param { number } count 购买药品数
 * @param { string } specification 规格
 * @param { string } dose 用法用量
 */
export type drugModel = {
  name: string;
  count: number;
  specification: string;
  dose: string;
};
