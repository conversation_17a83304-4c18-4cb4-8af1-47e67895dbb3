import request from '@/config/axios'

export interface InfoMemberVO {
  id: number
  missionId: number
  missionRole: number
  missionUid: number
  missionUserType: number
}

export interface InfoMemberPageReqVO extends PageParam {
  missionId?: number
  missionRole?: number
  missionUid?: number
  missionUserType?: number
  createTime?: Date[]
}

export interface InfoMemberExcelReqVO {
  missionId?: number
  missionRole?: number
  missionUid?: number
  missionUserType?: number
  createTime?: Date[]
}

// 查询参与任务的人员配置列表
export const getInfoMemberPageApi = async (params: InfoMemberPageReqVO) => {
  return await request.get({ url: '/mission/info-member/page', params })
}

// 查询参与任务的人员配置详情
export const getInfoMemberApi = async (id: number) => {
  return await request.get({ url: '/mission/info-member/get?id=' + id })
}

// 新增参与任务的人员配置
export const createInfoMemberApi = async (data: InfoMemberVO) => {
  return await request.post({ url: '/mission/info-member/create', data })
}

// 修改参与任务的人员配置
export const updateInfoMemberApi = async (data: InfoMemberVO) => {
  return await request.put({ url: '/mission/info-member/update', data })
}

// 删除参与任务的人员配置
export const deleteInfoMemberApi = async (id: number) => {
  return await request.delete({ url: '/mission/info-member/delete?id=' + id })
}

// 导出参与任务的人员配置 Excel
export const exportInfoMemberApi = async (params: InfoMemberExcelReqVO) => {
  return await request.download({ url: '/mission/info-member/export-excel', params })
}
