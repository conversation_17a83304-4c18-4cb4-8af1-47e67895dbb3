import request from '@/config/axios'

export interface FreeVisitVO {
  id: number
  sickId: number
  doctorId: number
  telemedicineTime: Date
  visitTime: Date
  expiredTime: Date
  status: number
  telemedicineId: number
  visitId: number
  visitType: number
}

export interface FreeVisitPageReqVO extends PageParam {
  sickId?: number
  doctorId?: number
  telemedicineTime?: Date[]
  visitTime?: Date[]
  expiredTime?: Date[]
  status?: number
  createTime?: Date[]
  telemedicineId?: number
  visitId?: number
  visitType?: number
}

export interface FreeVisitExcelReqVO {
  sickId?: number
  doctorId?: number
  telemedicineTime?: Date[]
  visitTime?: Date[]
  expiredTime?: Date[]
  status?: number
  createTime?: Date[]
  telemedicineId?: number
  visitId?: number
  visitType?: number
}

// 查询患者免费问诊关系列表
export const getFreeVisitPageApi = async (params: FreeVisitPageReqVO) => {
  return await request.get({ url: '/patient/free-visit/page', params })
}

// 查询患者免费问诊关系详情
export const getFreeVisitApi = async (id: number) => {
  return await request.get({ url: '/patient/free-visit/get?id=' + id })
}

// 新增患者免费问诊关系
export const createFreeVisitApi = async (data: FreeVisitVO) => {
  return await request.post({ url: '/patient/free-visit/create', data })
}

// 修改患者免费问诊关系
export const updateFreeVisitApi = async (data: FreeVisitVO) => {
  return await request.put({ url: '/patient/free-visit/update', data })
}

// 删除患者免费问诊关系
export const deleteFreeVisitApi = async (id: number) => {
  return await request.delete({ url: '/patient/free-visit/delete?id=' + id })
}

// 导出患者免费问诊关系 Excel
export const exportFreeVisitApi = async (params: FreeVisitExcelReqVO) => {
  return await request.download({ url: '/patient/free-visit/export-excel', params })
}

export const getFreeVisitByVisitApi = async (visitId: number, visitType: number) => {
  return await request.get({ url: `/patient/free-visit/list-visit?visitId=${visitId}&visitType=${visitType}` })
}
