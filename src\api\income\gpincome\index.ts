import request from '@/config/axios'

export interface IncomePayVO {
}

export interface IncomePayPageReqVO extends PageParam {
  gpId?: number
  type?: number
  objectId?: number
  createTime?: Date[]
  objectType?: number
}

export interface IncomePayExcelReqVO {
  gpId?: number
  type?: number
  objectId?: number
  createTime?: Date[]
  objectType?: number
}

// 查询村医收支列表
export const getIncomePayPageApi = async (params: IncomePayPageReqVO) => {
  return await request.get({ url: '/gp/income-pay/page', params })
}

// 查询村医收支详情
export const getIncomePayApi = async (id: number) => {
  return await request.get({ url: '/gp/income-pay/get?id=' + id })
}

// 新增村医收支
export const createIncomePayApi = async (data: IncomePayVO) => {
  return await request.post({ url: '/gp/income-pay/create', data })
}

// 修改村医收支
export const updateIncomePayApi = async (data: IncomePayVO) => {
  return await request.put({ url: '/gp/income-pay/update', data })
}

// 删除村医收支
export const deleteIncomePayApi = async (id: number) => {
  return await request.delete({ url: '/gp/income-pay/delete?id=' + id })
}

// 导出村医收支 Excel
export const exportIncomePayApi = async (params: IncomePayExcelReqVO) => {
  return await request.download({ url: '/gp/income-pay/export-excel', params })
}
