import { operateTypeEnum } from '@/common/enum'
import request from '@/config/axios'

export interface ImgsUrlVO {
  id: number
  type: number
  url: string
  sort: number
}

export interface ImgsUrlPageReqVO extends PageParam {
  type?: number
  url?: string
  sort?: number
  createTime?: Date[]
}

export interface ImgsUrlExcelReqVO {
  type?: number
  url?: string
  sort?: number
  createTime?: Date[]
}

// 查询图片url列表
export const getImgsUrlPageApi = async (params: ImgsUrlPageReqVO) => {
  return await request.get({ url: '/operate/imgs-url/page', params })
}

// 查询图片url详情
export const getImgsUrlApi = async (id: number) => {
  return await request.get({ url: '/operate/imgs-url/get?id=' + id })
}

// 新增图片url
export const createImgsUrlApi = async (data: ImgsUrlVO) => {
  return await request.post({ url: '/operate/imgs-url/create', data })
}

// 修改图片url
export const updateImgsUrlApi = async (data: ImgsUrlVO) => {
  return await request.put({ url: '/operate/imgs-url/update', data })
}

// 删除图片url
export const deleteImgsUrlApi = async (id: number) => {
  return await request.delete({ url: '/operate/imgs-url/delete?id=' + id })
}

// 导出图片url Excel
export const exportImgsUrlApi = async (params: ImgsUrlExcelReqVO) => {
  return await request.download({ url: '/operate/imgs-url/export-excel', params })
}


// 查询图片url详情
export const getImgsUrlListApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/imgs-url/list?ids=' + ids })
}

/** 根据类型和objectId获得图片url列表
 * @param { operateTypeEnum } 类型
 * @param { number } objectId
 */
export const getByType = async (type: operateTypeEnum, objectId: number) => {
  return await request.get({
    url: `/operate/imgs-url/list-imgsurl-by-type?type=${type}&objectId=${objectId}`
  })
}


export const getByTypeAndIds = async (type: operateTypeEnum, ids: number[]) => {
  return await request.get({
    url: `/operate/imgs-url/list-imgsurl-by-type-ids?type=${type}&ids=${ids}`
  })
}


