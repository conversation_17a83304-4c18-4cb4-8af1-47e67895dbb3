import { isEvaluateEnum, orderStatusEnum } from '../../enum/im';
import { visitTypeEnum } from './sendMessageModel';

/** 获取问诊订单完成情况
 * @param { orderStatusEnum } orderStatus 订单状态
 * @param { visitTypeEnum } visitType 问诊类型
 * @param { number } orderId 订单id
 * @param { number } visitEndTime 问诊结束时间
 * @param { isEvaluateEnum } isEvaluate 是否评价（1：已评价；2：未评价；）
 * @param { number } visitId 诊断id
 */
export type completionInfoModel = {
  visitStatus: orderStatusEnum;
  visitType: visitTypeEnum;
  orderId: number;
  visitEndTime: number;
  isEvaluate: isEvaluateEnum;
  visitId: number;
};
