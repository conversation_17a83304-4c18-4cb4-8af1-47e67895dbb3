import request from '@/config/axios'

export interface PharmacistIncomePayVO {
}

export interface PharmacistIncomePayPageReqVO extends PageParam {
  type?: number
  objectType?: number
  objectDesc?: string
  createTime?: Date[]
}

export interface PharmacistIncomePayExcelReqVO {
  type?: number
  objectType?: number
  objectDesc?: string
  createTime?: Date[]
}

// 查询医生收支列表
export const getPharmacistIncomePayPageApi = async (params: PharmacistIncomePayPageReqVO) => {
  return await request.get({ url: '/doctor/pharmacist-income-pay/page', params })
}

// 查询医生收支详情
export const getPharmacistIncomePayApi = async (id: number) => {
  return await request.get({ url: '/doctor/pharmacist-income-pay/get?id=' + id })
}

// 新增医生收支
export const createPharmacistIncomePayApi = async (data: PharmacistIncomePayVO) => {
  return await request.post({ url: '/doctor/pharmacist-income-pay/create', data })
}

// 修改医生收支
export const updatePharmacistIncomePayApi = async (data: PharmacistIncomePayVO) => {
  return await request.put({ url: '/doctor/pharmacist-income-pay/update', data })
}

// 删除医生收支
export const deletePharmacistIncomePayApi = async (id: number) => {
  return await request.delete({ url: '/doctor/pharmacist-income-pay/delete?id=' + id })
}

// 导出医生收支 Excel
export const exportPharmacistIncomePayApi = async (params: PharmacistIncomePayExcelReqVO) => {
  return await request.download({ url: '/doctor/pharmacist-income-pay/export-excel', params })
}
