import request from '@/config/axios'

export interface IllnessVO {
  id: number
  name: string
  parentId: number
  departId: number
  isCommon: number
  iconUrl: string
  sort: number
  about: string
}

export interface IllnessPageReqVO extends PageParam {
  name?: string
  parentId?: number
  createTime?: Date[]
}

export interface IllnessExcelReqVO {
  name?: string
  parentId?: number
  createTime?: Date[]
}

// 查询疾病信息列表
export const getIllnessPageApi = async (params: IllnessPageReqVO) => {
  return await request.get({ url: '/operate/illness/page', params })
}

// 查询疾病信息详情
export const getIllnessApi = async (id: number) => {
  return await request.get({ url: '/operate/illness/get?id=' + id })
}

// 新增疾病信息
export const createIllnessApi = async (data: IllnessVO) => {
  return await request.post({ url: '/operate/illness/create', data })
}

// 修改疾病信息
export const updateIllnessApi = async (data: IllnessVO) => {
  return await request.put({ url: '/operate/illness/update', data })
}

// 删除疾病信息
export const deleteIllnessApi = async (id: number) => {
  return await request.delete({ url: '/operate/illness/delete?id=' + id })
}

// 导出疾病信息 Excel
export const exportIllnessApi = async (params: IllnessExcelReqVO) => {
  return await request.download({ url: '/operate/illness/export-excel', params })
}

// 根据疾病id集合 获得疾病信息列表
export const getIllnessByIds = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/operate/illness/list?ids=' + ids })
}
