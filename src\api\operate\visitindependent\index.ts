import request from '@/config/axios'

export interface VisitIndependentVO {
  id: number
  visitType: number
  awardType: number
  status: number
  settlementIntervalStart: number
  settlementIntervalEnd: number
  jobTime: string
  userType: number
  rate: number
}

export interface NewVisitIndependentVO {
  visitType: number
  settlementIntervalStart: number
  settlementIntervalEnd: number
  doctorRate: number
  gpRate: number
  gpId: number
  doctorId: number
  status?: number
}

export interface VisitIndependentPageReqVO extends PageParam {
  visitType?: number
  awardType?: number
  status?: number
  settlementIntervalStart?: number
  settlementIntervalEnd?: number
  jobTime?: string
  createTime?: Date[]
  userType?: number
  rate?: number
}

export interface VisitIndependentExcelReqVO {
  visitType?: number
  awardType?: number
  status?: number
  settlementIntervalStart?: number
  settlementIntervalEnd?: number
  jobTime?: string
  createTime?: Date[]
  userType?: number
  rate?: number
}

// 查询问诊分账列表
export const getVisitIndependentPageApi = async (params: VisitIndependentPageReqVO) => {
  return await request.get({ url: '/operate/visit-independent/page', params })
}

// 查询问诊分账详情
export const getVisitIndependentApi = async (id: number) => {
  return await request.get({ url: '/operate/visit-independent/get?id=' + id })
}

// 新增问诊分账
export const createVisitIndependentApi = async (data: VisitIndependentVO) => {
  return await request.post({ url: '/operate/visit-independent/create', data })
}

// 修改问诊分账
export const updateVisitIndependentApi = async (data: VisitIndependentVO) => {
  return await request.put({ url: '/operate/visit-independent/update', data })
}

// 删除问诊分账
export const deleteVisitIndependentApi = async (id: number) => {
  return await request.delete({ url: '/operate/visit-independent/delete?id=' + id })
}

// 导出问诊分账 Excel
export const exportVisitIndependentApi = async (params: VisitIndependentExcelReqVO) => {
  return await request.download({ url: '/operate/visit-independent/export-excel', params })
}

// 查询问诊分账列表-问诊类型唯一
export const getVisitIndependentByVisitPageApi = async (params: VisitIndependentPageReqVO) => {
  return await request.get({ url: '/operate/visit-independent/page-by-visit', params })
}

// 获得药品分账列表
export const getVisitIndependentByVisitApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/visit-independent/list-by-visit?ids=' + ids })
}

// 新增问诊分账
export const createNewVisitIndependentApi = async (data: NewVisitIndependentVO) => {
  return await request.post({ url: '/operate/visit-independent/create-visit', data })
}


export const getVisitIndependentListApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/visit-independent/list?ids=' + ids })
}


// 修改问诊分账
export const updateNewVisitIndependentApi = async (data: NewVisitIndependentVO) => {
  return await request.put({ url: '/operate/visit-independent/update-visit', data })
}

// 删除问诊分账
export const deleteVisitNewIndependentApi = async (ids: number[]) => {
  return await request.delete({ url: '/operate/visit-independent/delete-visit?ids=' + ids })
}
