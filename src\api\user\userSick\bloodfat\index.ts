import request from '@/config/axios'

export interface BloodFatVO {
  id: number
  gpId: number
  sickId: number
  chol: number
  tg: number
  hdl: number
  ldl: number
  checkResult: number
  checkContent: string
  deviceCode: string
  measuringTime: Date
}

export interface BloodFatPageReqVO extends PageParam {
  gpId?: number
  sickId?: number
  chol?: number
  tg?: number
  hdl?: number
  ldl?: number
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

export interface BloodFatExcelReqVO {
  gpId?: number
  sickId?: number
  chol?: number
  tg?: number
  hdl?: number
  ldl?: number
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

// 查询胆固醇健康检查列表
export const getBloodFatPageApi = async (params: BloodFatPageReqVO) => {
  return await request.get({ url: '/patient/blood-fat/page', params })
}

// 查询胆固醇健康检查详情
export const getBloodFatApi = async (id: number) => {
  return await request.get({ url: '/patient/blood-fat/get?id=' + id })
}

// 新增胆固醇健康检查
export const createBloodFatApi = async (data: BloodFatVO) => {
  return await request.post({ url: '/patient/blood-fat/create', data })
}

// 修改胆固醇健康检查
export const updateBloodFatApi = async (data: BloodFatVO) => {
  return await request.put({ url: '/patient/blood-fat/update', data })
}

// 删除胆固醇健康检查
export const deleteBloodFatApi = async (id: number) => {
  return await request.delete({ url: '/patient/blood-fat/delete?id=' + id })
}

// 导出胆固醇健康检查 Excel
export const exportBloodFatApi = async (params: BloodFatExcelReqVO) => {
  return await request.download({ url: '/patient/blood-fat/export-excel', params })
}
