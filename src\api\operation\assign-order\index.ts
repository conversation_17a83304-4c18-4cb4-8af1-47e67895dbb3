import request from '@/config/axios'

export interface AssignOrderVO {
  id: number
  name: string
  about: string
  sort: number
}

export interface AssignOrderPageReqVO extends PageParam {
}

export interface AssignOrderExcelReqVO {
}

// 查询会诊派单列表
export const getAssignOrderPageApi = async (params: AssignOrderPageReqVO) => {
  return await request.get({ url: '/operate/assign-order/page', params })
}

// 查询会诊派单详情
export const getAssignOrderApi = async (id: number) => {
  return await request.get({ url: '/operate/assign-order/get?id=' + id })
}

// 新增会诊派单
export const createAssignOrderApi = async (data: AssignOrderVO) => {
  return await request.post({ url: '/operate/assign-order/create', data })
}

// 修改会诊派单
export const updateAssignOrderApi = async (data: AssignOrderVO) => {
  return await request.put({ url: '/operate/assign-order/update', data })
}

// 删除会诊派单
export const deleteAssignOrderApi = async (id: number) => {
  return await request.delete({ url: '/operate/assign-order/delete?id=' + id })
}

// 导出会诊派单 Excel
export const exportAssignOrderApi = async (params: AssignOrderExcelReqVO) => {
  return await request.download({ url: '/operate/assign-order/export-excel', params })
}
