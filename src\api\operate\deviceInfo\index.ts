import request from '@/config/axios'

export interface DeviceInfoVO {
  id: number
  deviceCode: string
  deviceInfoId: string
  deviceVersion: string
  deviceItemList: any[]
  about: string
  gpId: number
  gpName: string
  status: number
  provinceCode: number
  provinceName: string
  cityCode: number
  cityName: string
}

export interface DeviceInfoPageReqVO extends PageParam {
  deviceCode?: string
  deviceVersion?: string
  about?: string
  gpId?: number
  gpName?: string
  status?: number
  provinceCode?: number
  provinceName?: string
  cityCode?: number
  cityName: string
  createTime?: Date[]
}

export interface DeviceInfoExcelReqVO {
  deviceCode?: string
  deviceVersion?: string
  about?: string
  gpId?: number
  gpName?: string
  status?: number
  provinceCode?: number
  cityCode?: number
  provinceName?: string
  cityName: string
  createTime?: Date[]
}

// 查询设备信息列表
export const getDeviceInfoPageApi = async (params: DeviceInfoPageReqVO) => {
  return await request.get({ url: '/gp/device-info/page', params })
}

// 查询设备信息详情
export const getDeviceInfoApi = async (id: number) => {
  return await request.get({ url: '/gp/device-info/get?id=' + id })
}

// 新增设备信息
export const createDeviceInfoApi = async (data: DeviceInfoVO) => {
  return await request.post({ url: '/gp/device-info/create', data })
}

// 修改设备信息
export const updateDeviceInfoApi = async (data: DeviceInfoVO) => {
  return await request.put({ url: '/gp/device-info/update', data })
}

// 删除设备信息
export const deleteDeviceInfoApi = async (id: number) => {
  return await request.delete({ url: '/gp/device-info/delete?id=' + id })
}

// 导出设备信息 Excel
export const exportDeviceInfoApi = async (params: DeviceInfoExcelReqVO) => {
  return await request.download({ url: '/gp/device-info/export-excel', params })
}

export const getDeviceInfoListAllApi = async () => {
  return await request.get({ url: '/gp/device-info/list-all' })
}
