<template>
  <ContentWrap>
    <!-- 列表 -->
    <vxe-grid ref="xGrid" v-bind="gridOptions" class="xtable-scrollbar">
      <template #toolbar_buttons>
        <!-- 操作：新增 -->
        <XButton
          type="primary"
          preIcon="ep:zoom-in"
          :title="t('action.add')"
          v-hasPermi="['patient:user-sick:create']"
          @click="handleCreate()"
        />
        <!-- 操作：导出 -->
        <XButton
          type="warning"
          preIcon="ep:download"
          :title="t('action.export')"
          v-hasPermi="['patient:user-sick:export']"
          @click="handleExport()"
        />
      </template>
      <template #actionbtns_default="{ row }">
        <!-- 操作：修改 -->
        <!-- <XTextButton
          preIcon="ep:edit"
          :title="t('action.edit')"
          v-hasPermi="['patient:user-sick:update']"
          @click="handleUpdate(row.id)"
        /> -->
        <!-- 操作：详情 -->
        <XTextButton
          preIcon="ep:view"
          :title="t('action.detail')"
          v-hasPermi="['patient:user-sick:query']"
          @click="handleDetail(row)"
        />
        <!-- 操作：删除 -->
        <XTextButton
          preIcon="ep:delete"
          :title="t('action.del')"
          v-hasPermi="['patient:user-sick:delete']"
          @click="handleDelete(row.id)"
        />
      </template>

      <template #areaid_template="{ row }">
        <el-tag v-if="row.districtCode === 0" type="danger">不存在</el-tag>
        <el-tag v-else type="success"> {{ row.areaCode }} </el-tag>
      </template>
      <template #gpAreaCode="{ row }">
        <el-tag v-if="!row.gpAreaCode" type="danger">不存在</el-tag>
        <el-tag v-else type="success"> {{ row.gpAreaCode }} </el-tag>
      </template>
    </vxe-grid>
  </ContentWrap>
  <!-- 弹窗 -->
  <XModal id="userSickModel" :loading="modelLoading" v-model="modelVisible" :title="modelTitle">
    <!-- 表单：添加/修改 -->
    <Form
      ref="formRef"
      v-if="['create', 'update'].includes(actionType)"
      :schema="allSchemas.formSchema"
      :rules="rules"
    />
    <!-- 表单：详情 -->
    <MenuTab
      v-if="actionType === 'detail'"
      :sickId="detailSickId"
      :default-descriptions="{
        schema: allSchemas.detailSchema,
        data: detailData
      }"
    />
    <template #footer>
      <!-- 按钮：保存 -->
      <XButton
        v-if="['create', 'update'].includes(actionType)"
        type="primary"
        :title="t('action.save')"
        :loading="actionLoading"
        @click="submitForm()"
      />
      <!-- 按钮：关闭 -->
      <XButton :loading="actionLoading" :title="t('dialog.close')" @click="modelVisible = false" />
    </template>
  </XModal>
</template>
<script setup lang="ts" name="UserSick">
// 全局相关的 import
import { ref, unref } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useVxeGrid } from '@/hooks/web/useVxeGrid'
import { VxeGridInstance } from 'vxe-table'
import { FormExpose } from '@/components/Form'
import { MenuTab } from './components/index'
// 业务相关的 import
import { rules, allSchemas } from './userSick.data'
import * as UserSickApi from '@/api/user/userSick'
import { getUserSickListApi, pageInit } from '.'
import { isArray } from '@/utils/is'
import {  getHandleDetail } from './index'
// import { ageTrnslate } from '@/utils/ageTranslate'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

// 列表相关的变量
const xGrid = ref<VxeGridInstance>() // 列表 Grid Ref
const { gridOptions, getList, deleteData, exportList } = useVxeGrid<UserSickApi.UserSickVO>({
  allSchemas: allSchemas,
  getListApi: getUserSickListApi,
  deleteApi: UserSickApi.deleteUserSickApi,
  exportListApi: UserSickApi.exportUserSickAsyncApi
})

// 弹窗相关的变量
const modelVisible = ref(false) // 是否显示弹出层
const modelTitle = ref('edit') // 弹出层标题
const modelLoading = ref(false) // 弹出层loading
const actionType = ref('') // 操作按钮的类型
const actionLoading = ref(false) // 按钮 Loading
const formRef = ref<FormExpose>() // 表单 Ref
const detailData = ref() // 详情 Ref
const detailSickId = ref(0)



//异步加载数据
onMounted(() => {
  pageInit()
})

// 设置标题
const setDialogTile = (type: string) => {
  modelLoading.value = true
  modelTitle.value = t('action.' + type)
  actionType.value = type
  modelVisible.value = true
}

// 新增操作
const handleCreate = () => {
  setDialogTile('create')
  modelLoading.value = false
}

// 导出操作
const handleExport = async () => {
  await exportList(xGrid, '就诊用户信息.xls')
}

// 修改操作
// const handleUpdate = async (rowId: number) => {
//   setDialogTile('update')
//   // 设置数据
//   const res = await getHandleUpdate(rowId)
//   unref(formRef)?.setValues(res)
//   modelLoading.value = false
// }

// 详情操作
const handleDetail = async (row: any) => {
  setDialogTile('detail')
  const res = await getHandleDetail(row)
  detailSickId.value = row.id
  detailData.value = res
  modelLoading.value = false
}

// 删除操作
const handleDelete = async (rowId: number) => {
  await deleteData(xGrid, rowId)
}

// 提交按钮
const submitForm = async () => {
  const elForm = unref(formRef)?.getElFormRef()
  if (!elForm) return
  elForm.validate(async (valid) => {
    if (valid) {
      actionLoading.value = true
      // 提交请求
      try {
        const data = unref(formRef)?.formModel as UserSickApi.UserSickVO

        if (data.areaCode && isArray(data.areaCode) && data.areaCode.length > 0) {
          data.areaCode = data.areaCode.pop() as number
        }
        else{
            data.areaCode=undefined
        }
        if (actionType.value === 'create') {
          await UserSickApi.createUserSickApi(data)
          message.success(t('common.createSuccess'))
        } else {
          await UserSickApi.updateUserSickApi(data)
          message.success(t('common.updateSuccess'))
        }
        modelVisible.value = false
      } finally {
        actionLoading.value = false
        // 刷新列表
        await getList(xGrid)
      }
    }
  })
}
</script>
