import { NIMMessage } from '@yxim/nim-web-sdk/dist/SDK/NIM_Web_SDK/nim/MessageInterface';
import { reqStatusEnum, roomType, rtcType, sexEnum, userType } from '../../enum/im';
import { pageItemModel } from './resModel';
// import { bodyModel } from '@/common/model/resModel';
import { customMsgEnum } from './sendMessageModel';

export type reqStatusModel = {
  status: reqStatusEnum;
};

/** 自定义 im 模型扩展 */
export type CostomNIMMessage = NIMMessage & {
  fileBlob?: Blob;
  msgOpe?: number;
  msgCustomType?: string;
};

/** 音频转文本的模型 */
export type audioToTextModel = {
  audioToText: {
    url: string;
  };
  text: string;
  url: string;
};

/** 房间数据模型
 * @param { string } sessionId sid
 * @param { string } accid account 账户
 * @param { string } name 昵称
 * @param { string } props
 * @param { string } icon 头像
 * @param { string } sign 签名
 * @param { string } email
 * @param { string } birth 生日
 * @param { string } mobile 手机
 * @param { sexEnum } gender 性别
 * @param { string } ex 拓展 jsonstring
 * @param { string } account_uid 云信 uid
 * @param { number } user_id 业务用户id
 * @param { userType } user_type 业务用户类型
 */
export type getSessionAccountModel = {
  accid?: string;
  token?: string;
  name?: string;
  props?: string;
  icon?: string;
  sign?: string;
  email?: string;
  birth?: string;
  mobile?: string;
  gender?: sexEnum;
  ex?: string;
  account_uid?: number;
  user_id?: number;
  user_type?: userType;
};

/**
 * 根据会话获得聊天消息记录
 * @param {number} pageNo 页码，从 1 开始
 * @param {number} pageSize 每页条数，最大值为 100
 * @param {string} sessionId
 * @param {string} accid
 * @param {number} startTimestamp
 * @param {number} endTimestamp
 * @returns
 */
export interface GetMessageBySessionIdParams {
  pageNo?: number;
  pageSize?: number;
  sessionId: string;
  accid?: string;
  startTimestamp?: number;
  endTimestamp?: number;
}

/** 聊天历史记录数据（data平级保留变化） */
export interface getMsgBySessionIdReturnModel {
  data: pageItemModel<CostomNIMMessage>;
}
// 聊天历史记录数据
export interface GetMessageBySessionIdRes {
  msgOpe: number;
  msgCustomType: string;
  from: string;
  to: string;
  text: string;
  desc: string;
  attach: string;
  idClient: string;
  idServer: number;
  fromNick: string;
  type: string;
  custom: string;
  time: number;
  fromDeviceId: string;
  fromClientType: string;
  sid: string;
  subType: number;
  file: string;
  content: string;
}

/** 音频消息转文字翻译拓展 */
// 参数接口
export interface CreateAudioTransParams {
  msgClientId?: string;
  lang?: string;
  trans?: string;
}

/**  云端音频转文字（阿里云）
 * @param { string } fileLink 阿里云文件地址
 * @param { string } taskId 创建云端任务获取的staskId
 */
export interface TranTaskModel {
  fileLink: string;
  taskId?: string;
}

export interface TranTaskReturnModel {
  Sentences: TranTaskSentencesModel[];
}

export interface TranTaskSentencesModel {
  EndTime: number;
  SilenceDuration: number;
  SpeakerId: string;
  BeginTime: number;
  Text: string;
  ChannelId: number;
  SpeechRate: number;
  EmotionValue: number;
}

// "Sentences":[{"EndTime":2790,"SilenceDuration":0,"SpeakerId":"1","BeginTime":0,"Text":"一二三四五。","ChannelId":0,"SpeechRate":107,"EmotionValue":6.5}]

/** 自己给自己发消息
 * @param { customMsgEnum } type 消息类型
 * @param { string } from 发起方id
 * @param { string } to 接收方ID
 * @param { string } data json 字符串模型
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
type toSelfImModel = {
  type: customMsgEnum;
  from: string;
  to: string;
  data: string;
};

/** 信令自定义模型
 * @param { string } channelName 房间名
 * @param { string } channelId 房间id（相当于sid）
 * @param { rtcUserModel[] } user 房间所有人列表
 * @param { rtcType } rtcType rtc类型
 * @param { roomType } roomType 房间类型
 * @param { signalingCustomMsgEnum } customMsgType 信令自定义消息类型
 */
// eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
type RModel = {
  channelName: string;
  channelId: string;
  user: rtcUserModel[];
  rtcType: rtcType;
  roomType: roomType;
  customMsgType:signalingCustomMsgEnum;
};

/** 信令自定义消息类型
* @param { number } inviteRTC 邀请别人加入RTC
* @param { number } joinRTC 加人RTC房间
* @param { number } reiectRTC 拒绝加入RTC房间
* @param { number } busyLineRTC 忙线
* @param { number } cancalInviteRTC 取消邀请
*/
enum signalingCustomMsgEnum {
  inviteRTC = 1,
  joinRTC,
  rejectRTC,
  busyLineRTC,
  cancalInviteRTC
}

/** 信令自定义信息的用户模型
 * @param { string } icon 头像
 * @param { string } name 用户名
 * @param { string } accid 云信id
 * @param { number } uid 业务id
 */
type rtcUserModel = {
  icon: string;
  name: string;
  accid: string;
  uid: number;
};
