import request from '@/config/axios'

export interface BloodPressureVO {
  id: number
  gpId: number
  sickId: number
  part: number
  dbp: number
  sbp: number
  deviceCode: string
  measuringTime: Date
}

export interface BloodPressurePageReqVO extends PageParam {
  gpId?: number
  sickId?: number
  part?: number
  dbp?: number
  sbp?: number
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

export interface BloodPressureExcelReqVO {
  gpId?: number
  sickId?: number
  part?: number
  dbp?: number
  sbp?: number
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

// 查询血压健康检查结果列表
export const getBloodPressurePageApi = async (params: BloodPressurePageReqVO) => {
  return await request.get({ url: '/patient/blood-pressure/page', params })
}

// 查询血压健康检查结果详情
export const getBloodPressureApi = async (id: number) => {
  return await request.get({ url: '/patient/blood-pressure/get?id=' + id })
}

// 新增血压健康检查结果
export const createBloodPressureApi = async (data: BloodPressureVO) => {
  return await request.post({ url: '/patient/blood-pressure/create', data })
}

// 修改血压健康检查结果
export const updateBloodPressureApi = async (data: BloodPressureVO) => {
  return await request.put({ url: '/patient/blood-pressure/update', data })
}

// 删除血压健康检查结果
export const deleteBloodPressureApi = async (id: number) => {
  return await request.delete({ url: '/patient/blood-pressure/delete?id=' + id })
}

// 导出血压健康检查结果 Excel
export const exportBloodPressureApi = async (params: BloodPressureExcelReqVO) => {
  return await request.download({ url: '/patient/blood-pressure/export-excel', params })
}
