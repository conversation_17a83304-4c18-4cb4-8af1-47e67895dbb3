import request from '@/config/axios'

// 获得医生认证分页
export const getUserPageApi = async (params) => {
  return await request.get({ url: '/doctor/user/unauth-page', params })
}

// 审核通过
export const approvedAuthenticatedApi = async (data) => {
  return await request.put({ url: '/doctor/user/put-auth-authenticated', data })
}

// 审核驳回
export const approvedFailedApi = async (data) => {
  return await request.put({ url: '/doctor/user/put-auth-failed', data })
}
