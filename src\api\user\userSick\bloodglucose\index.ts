import request from '@/config/axios'

export interface BloodGlucoseVO {
  id: number
  gpId: number
  sickId: number
  referenceType: number
  glu: number
  checkResult: number
  checkContent: string
  deviceCode: string
  measuringTime: Date
}

export interface BloodGlucosePageReqVO extends PageParam {
  gpId?: number
  sickId?: number
  referenceType?: number
  glu?: number
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

export interface BloodGlucoseExcelReqVO {
  gpId?: number
  sickId?: number
  referenceType?: number
  glu?: number
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

// 查询血糖健康检查结果列表
export const getBloodGlucosePageApi = async (params: BloodGlucosePageReqVO) => {
  return await request.get({ url: '/patient/blood-glucose/page', params })
}

// 查询血糖健康检查结果详情
export const getBloodGlucoseApi = async (id: number) => {
  return await request.get({ url: '/patient/blood-glucose/get?id=' + id })
}

// 新增血糖健康检查结果
export const createBloodGlucoseApi = async (data: BloodGlucoseVO) => {
  return await request.post({ url: '/patient/blood-glucose/create', data })
}

// 修改血糖健康检查结果
export const updateBloodGlucoseApi = async (data: BloodGlucoseVO) => {
  return await request.put({ url: '/patient/blood-glucose/update', data })
}

// 删除血糖健康检查结果
export const deleteBloodGlucoseApi = async (id: number) => {
  return await request.delete({ url: '/patient/blood-glucose/delete?id=' + id })
}

// 导出血糖健康检查结果 Excel
export const exportBloodGlucoseApi = async (params: BloodGlucoseExcelReqVO) => {
  return await request.download({ url: '/patient/blood-glucose/export-excel', params })
}
