import request from '@/config/axios'

export interface VisitOrderVO {
}

export interface VisitOrderPageReqVO extends PageParam {
  orderNum?: string
  doctorName?: string
  orderType?: number
  payOrderId?: number
}

export interface VisitOrderExcelReqVO {
  orderNum?: string
  doctorName?: string
  orderType?: number
  payOrderId?: number
}

// 查询患者问诊订单列表
export const getVisitOrderPageApi = async (params: VisitOrderPageReqVO) => {
  return await request.get({ url: '/patient/visit-order/page', params })
}

// 查询患者问诊订单详情
export const getVisitOrderApi = async (id: number) => {
  return await request.get({ url: '/patient/visit-order/get?id=' + id })
}

// 新增患者问诊订单
export const createVisitOrderApi = async (data: VisitOrderVO) => {
  return await request.post({ url: '/patient/visit-order/create', data })
}

// 修改患者问诊订单
export const updateVisitOrderApi = async (data: VisitOrderVO) => {
  return await request.put({ url: '/patient/visit-order/update', data })
}

// 删除患者问诊订单
export const deleteVisitOrderApi = async (id: number) => {
  return await request.delete({ url: '/patient/visit-order/delete?id=' + id })
}

// 导出患者问诊订单 Excel
export const exportVisitOrderApi = async (params: VisitOrderExcelReqVO) => {
  return await request.download({ url: '/patient/visit-order/export-excel', params })
}

// 查询orderNumbers
export const getOrderListByOrderNumbers = async (orderNumbers: Array<number>) => {
  return await request.get({ url: '/patient/visit-order/find-order-list-by-orderNumbers?orderNumbers=' + orderNumbers })
}

