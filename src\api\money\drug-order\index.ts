import request from '@/config/axios'

export interface DrugOrderVO {
}

export interface DrugOrderPageReqVO extends PageParam {
  receiverPhone?: string
  orderType?: number
  sickName?: string
  status?: number
  payOrderNo?: string
  rxId?: number
  rxUserId?: number
  rxUser?: string
  rxName?:string
}

export interface DrugOrderExcelReqVO {
  receiverPhone?: string
  orderType?: number
  sickName?: string
  status?: number
  payOrderNo?: string
  rxId?: number
  rxUserId?: number
  rxUser?: string
}

// 查询患者药品订单列表
export const getDrugOrderPageApi = async (params: DrugOrderPageReqVO) => {
  return await request.get({ url: '/patient/drug-order/page', params })
}

// 查询患者药品订单详情
export const getDrugOrderApi = async (id: number) => {
  return await request.get({ url: '/patient/drug-order/get?id=' + id })
}

// 新增患者药品订单
export const createDrugOrderApi = async (data: DrugOrderVO) => {
  return await request.post({ url: '/patient/drug-order/create', data })
}

// 修改患者药品订单
export const updateDrugOrderApi = async (data: DrugOrderVO) => {
  return await request.put({ url: '/patient/drug-order/update', data })
}

// 删除患者药品订单
export const deleteDrugOrderApi = async (id: number) => {
  return await request.delete({ url: '/patient/drug-order/delete?id=' + id })
}

// 导出患者药品订单 Excel
export const exportDrugOrderApi = async (params: DrugOrderExcelReqVO) => {
  return await request.download({ url: '/patient/drug-order/export-excel', params })
}
