export type RoomAccountVO = {
  id: number
  accountId: number
  userId: number
  userType: number
  userUuid: string
  userRole: string
  roomId: number
  roomUuid: string
}

export type RoomAccountPageReqVO = {
  accountId: number
  userId: number
  userType: number
  userUuid: string
  userRole: string
  roomId: number
  roomUuid: string
  createTime: string
}

export type RoomAccountExcelReqVO = {
  accountId: number
  userId: number
  userType: number
  userUuid: string
  userRole: string
  roomId: number
  roomUuid: string
  createTime: string
}