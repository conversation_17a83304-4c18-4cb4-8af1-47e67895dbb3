import request from '@/config/axios'

export interface IncomeCostVO {
}

export interface IncomeCostPageReqVO extends PageParam {
  type?: number
  payOrderNo?: string
  userType?: number
  incomePayType?: number
  objectType?: number
}

export interface IncomeCostExcelReqVO {
  type?: number
  payOrderNo?: string
  userType?: number
  incomePayType?: number
  objectType?: number
}

// 查询收入详情记录列表
export const getIncomeCostPageApi = async (params: IncomeCostPageReqVO) => {
  return await request.get({ url: '/operate/income-cost/page', params })
}

// 查询收入详情记录详情
export const getIncomeCostApi = async (id: number) => {
  return await request.get({ url: '/operate/income-cost/get?id=' + id })
}

// 新增收入详情记录
export const createIncomeCostApi = async (data: IncomeCostVO) => {
  return await request.post({ url: '/operate/income-cost/create', data })
}

// 修改收入详情记录
export const updateIncomeCostApi = async (data: IncomeCostVO) => {
  return await request.put({ url: '/operate/income-cost/update', data })
}

// 删除收入详情记录
export const deleteIncomeCostApi = async (id: number) => {
  return await request.delete({ url: '/operate/income-cost/delete?id=' + id })
}

// 导出收入详情记录 Excel
export const exportIncomeCostApi = async (params: IncomeCostExcelReqVO) => {
  return await request.download({ url: '/operate/income-cost/export-excel', params })
}
