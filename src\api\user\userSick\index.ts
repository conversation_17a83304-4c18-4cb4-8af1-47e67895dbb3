import request from '@/config/axios'

export interface UserSickVO {
  id: number
  name: string
  idCard: string
  birthday: Date
  address: string
  phone: string
  isDefault: boolean
  patientId: number
  sex: number
  areaCode?: number
  relationType: number
  status: number
}

export interface UserSickPageReqVO extends PageParam {
  name?: string
  patientId?: number
  sex?: number
  areaCode?: number
  relationType?: number
  status?: number
}

export interface UserSickExcelReqVO {
  name?: string
  patientId?: number
  sex?: number
  areaCode?: number
  relationType?: number
  status?: number
}

// 查询就诊用户信息列表
export const getUserSickPageApi = async (params: UserSickPageReqVO) => {
  return await request.get({ url: '/patient/user-sick/page', params })
}

// 查询就诊用户信监管列表
export const getUserSickPageSuperviseApi = async (params: UserSickPageReqVO) => {
  return await request.get({ url: '/patient/user-sick/page-supervise', params })
}

// 查询就诊用户信息详情
export const getUserSickApi = async (id: number) => {
  return await request.get({ url: '/patient/user-sick/get?id=' + id })
}

// 新增就诊用户信息
export const createUserSickApi = async (data: UserSickVO) => {
  return await request.post({ url: '/patient/user-sick/create', data })
}

// 修改就诊用户信息
export const updateUserSickApi = async (data: UserSickVO) => {
  return await request.put({ url: '/patient/user-sick/update', data })
}

// 删除就诊用户信息
export const deleteUserSickApi = async (id: number) => {
  return await request.delete({ url: '/patient/user-sick/delete?id=' + id })
}

// 导出就诊用户信息 Excel
export const exportUserSickApi = async (params: UserSickExcelReqVO) => {
  return await request.download({ url: '/patient/user-sick/export-excel', params })
}

// 根据患者用户id集合 获得患者信息
export const getUserSickByIds = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/patient/user-sick/list?ids=' + ids })
}

// 根据患者用户id集合 获得患者信息分页
export const getUserSickPageByIds = async (
  ids: Array<number>,
  pageNo: number,
  pageSize: number
) => {
  return await request.get({
    url: `/patient/user-sick/find-page-by-ids?ids=${ids}&pageNo=${pageNo}&pageSize=${pageSize}`
  })
}


