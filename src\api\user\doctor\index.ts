import request from '@/config/axios'

export interface UserVO {
  id: number
  account: string
  password: string
  name: string
  birthday: Date
  phone: string
  headImg: string
  hospitalDepartId: number
  officeHolderCode: string
  beGoodAt: string
  about: string
  sex: number
  doctorType: number
  hospitalId: number
  hospitalDepart: string
  idCard: string
  departId: number
  districtCode?: number | number[]
  officeHolderId: number
  rankId: number
  rankName: string
  status: number
  isAuth: number
  visitCount: number
  evaAvg: number
  evaCount: number
  score: number
  receptionDuration: number
  certificationUrl: string
  doctorSealUrl: string
  idcardFrontUrl: string
  idcardReverseUrl: string
  officeHolderUrl: string
  practisingUrl: string
  authContent: string
  userDetail: UserDetailVO
}

export interface UserDetailVO {
  doctorId?: number
  certificationUrl?: string
  doctorSealUrl?: string
  idcardFrontUrl?: string
  idcardReverseUrl?: string
  officeHolderUrl?: string
  practisingUrl?: string
}

export interface UserPageReqVO extends PageParam {
  account?: string
  name?: string
  phone?: string
  sex?: number
  doctorType?: number
  hospitalId?: number
  hospitalDepart?: string
  idCard?: string
  departId?: number
  districtCode?: number
  officeHolderId?: number
  rankId?: number
  rankName?: string
  status?: number
  isAuth?: number
  visitCount?: number
  evaAvg?: number
  evaCount?: number
  score?: number
  receptionDuration?: number
  districtName?: string
}

export interface UserExcelReqVO {
  account?: string
  name?: string
  phone?: string
  sex?: number
  doctorType?: number
  hospitalId?: number
  hospitalDepart?: string
  idCard?: string
  departId?: number
  districtCode?: number
  officeHolderId?: number
  rankId?: number
  rankName?: string
  status?: number
  isAuth?: number
  visitCount?: number
  evaAvg?: number
  evaCount?: number
  score?: number
  receptionDuration?: number
}

// 查询医生信息列表
export const getUserPageApi = async (params: UserPageReqVO) => {
  return await request.get({ url: '/doctor/user/page', params })
}

// 查询医生信息监管列表
export const getUserPageSuperviseApi = async (params: any) => {
  return await request.get({ url: '/doctor/user/page-supervise', params })
}

export const getUserPageAuthApi = async (params: UserPageReqVO) => {
  return await request.get({ url: '/doctor/user/page-auth', params })
}

// 查询医生信息详情
export const getUserApi = async (id: number) => {
  return await request.get({ url: '/doctor/user/get?id=' + id })
}

// 新增医生信息
export const createUserApi = async (data: UserVO) => {
  return await request.post({ url: '/doctor/user/create', data })
}

// 修改医生信息
export const updateUserApi = async (data: UserVO) => {
  return await request.put({ url: '/doctor/user/update', data })
}

// 医生对接-撤销对接
export const updateUserSuperviseApi = async (data) => {
  return await request.put({ url: '/doctor/user/update-supervise-status', data })
}

// 删除医生信息
export const deleteUserApi = async (id: number) => {
  return await request.delete({ url: '/doctor/user/delete?id=' + id })
}

// 导出医生信息 Excel
export const exportUserApi = async (params: UserExcelReqVO) => {
  return await request.download({ url: '/doctor/user/export-excel', params })
}

/** 通过医生id获取费用设置
 *
 * @param { number } doctorId 医生id
 * @returns
 */
export const findPaySettingById = async (doctorId: number) => {
  return await request.get({
    url: '/doctor/user-setting/find-pay-setting-by-id?doctorId=' + doctorId
  })
}

// 根据用户id集合 获得信息
export const getUserByIds = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/doctor/user/list?ids=' + ids })
}

// 医师审核
export const updateAuthAuthenticatedApi = async (data: UserVO) => {
  return await request.put({ url: '/doctor/user/put-auth-authenticated', data })
}

// 医生驳回
export const updateAuthFailedApi = async (data: UserVO) => {
  return await request.put({ url: '/doctor/user/put-auth-failed', data })
}

// 医生置顶
export const updateRecommendUserApi = async (data: UserVO) => {
  return await request.put({ url: '/doctor/user/recommend', data })
}

// 获得医生评分信息
export const getDoctorScoreByDoctorId = async (doctorId: number) => {
  return await request.get({
    url: `/doctor/score/get-doctorscore-by-doctorid?doctorId=${doctorId}`
  })
}

// 获得医生会诊评分信息
export const getConsultationScoreByDoctorId = async (data) => {
  return await request.post({
    url: `/doctor/score/get-consultation-score-by-doctorid`,
    data
  })
}

// 获得医生疗效评分信息
export const getCurativeEffectScoreByDoctorId = async (data) => {
  return await request.post({
    url: `/doctor/score/get-curative-effect-score-by-doctorid`,
    data
  })
}

// 获得医生应答评分信息
export const getAckScoreByDoctorId = async (data) => {
  return await request.post({
    url: `/doctor/score/get-ack-score-by-doctorid`,
    data
  })
}

// 等级变更-手动调级
export const levelChangeManual = async (watchId) => {
  return await request.put({ url: `/doctor/score/level-change-manual?watchId=${watchId}` })
}

// 获得医生等级变更分页列表
export const getLevelChangePage = async (params) => {
  return await request.get({ url: '/doctor/score/level-change-page', params })
}

// 等级变更-手动调级
export const resetDoctorPasswordApi = async (data) => {
  return await request.put({ url: 'doctor/user/modify-password', data })
}

// 获得医生会诊调度记录明细
export const dispatchRecordDetail = async (params) => {
  return await request.get({ url: '/doctor/telemedicine/dispatchRecordDetail', params })
}

// 医生上下线
export const updownChange = async (data) => {
  return await request.put({ url: `doctor/user/updown`, data })
}

// 医生显示隐藏
export const showhideChange = async (data) => {
  return await request.put({ url: `doctor/user/showhide`, data })
}
