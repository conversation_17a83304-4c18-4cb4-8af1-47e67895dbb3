import request from '@/config/axios'

export interface BloodEcgVO {
  id: number
  gpId: number
  sickId: number
  ecgImg: string
  checkResult: number
  checkContent: string
  deviceCode: string
  measuringTime: Date
}

export interface BloodEcgPageReqVO extends PageParam {
  gpId?: number
  sickId?: number
  ecgImg?: string
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

export interface BloodEcgExcelReqVO {
  gpId?: number
  sickId?: number
  ecgImg?: string
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

// 查询心电图健康检查结果列表
export const getBloodEcgPageApi = async (params: BloodEcgPageReqVO) => {
  return await request.get({ url: '/patient/blood-ecg/page', params })
}

// 查询心电图健康检查结果详情
export const getBloodEcgApi = async (id: number) => {
  return await request.get({ url: '/patient/blood-ecg/get?id=' + id })
}

// 新增心电图健康检查结果
export const createBloodEcgApi = async (data: BloodEcgVO) => {
  return await request.post({ url: '/patient/blood-ecg/create', data })
}

// 修改心电图健康检查结果
export const updateBloodEcgApi = async (data: BloodEcgVO) => {
  return await request.put({ url: '/patient/blood-ecg/update', data })
}

// 删除心电图健康检查结果
export const deleteBloodEcgApi = async (id: number) => {
  return await request.delete({ url: '/patient/blood-ecg/delete?id=' + id })
}

// 导出心电图健康检查结果 Excel
export const exportBloodEcgApi = async (params: BloodEcgExcelReqVO) => {
  return await request.download({ url: '/patient/blood-ecg/export-excel', params })
}
