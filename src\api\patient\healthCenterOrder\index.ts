import request from '@/config/axios'

// 卫生院订单分页查询 VO
export interface HealthCenterOrderPageReqVO {
  pageNo: number
  pageSize: number
  orderNum?: string
  patientId?: number
  doctorId?: number
  sickId?: number
  doctorName?: string
  fromDoctorId?: number
  fromDoctorName?: string
  orderType?: number
  sickName?: string
  rxType?: number
  rxId?: number
  rxName?: string
  status?: number
  createTime?: [Date, Date]
  fromDoctorHospitalId?: string
  drugName?: string
  barcode?: string
}

// 卫生院订单导出 VO
export interface HealthCenterOrderExportReqVO {
  objectId?: number
  orderNum?: string
  patientId?: number
  sickId?: number
  doctorId?: number
  doctorName?: string
  fromDoctorId?: number
  fromDoctorName?: string
  orderType?: number
  orderPrice?: number
  sickName?: string
  sickSex?: number
  sickAge?: number
  receiverName?: string
  receiverArea?: string
  receiverAddress?: string
  receiverPhone?: string
  rxType?: number
  rxId?: number
  rxName?: string
  status?: number
  logisticsType?: number
  logisticsNo?: string
  logisticsComId?: string
  createTime?: [Date, Date]
  fromDoctorHospitalId?: string
  drugName?: string
  barcode?: string
}

// 卫生院订单药品 VO
export interface HealthCenterOrderDrugVO {
  drugId: number
  barCode: string
  drugName: string
  count: number
  weight: number
  unitPrice: number
}

// 卫生院订单响应 VO
export interface HealthCenterOrderRespVO {
  id: number
  objectId: number
  orderNum: string
  patientId: number
  doctorId: number
  doctorName: string
  fromDoctorId: number
  fromDoctorName: string
  fromDoctorHospital: string
  orderType: number
  orderPrice: number
  sickId: number
  sickName: string
  sickSex: number
  sickAge: number
  receiverName: string
  receiverArea: string
  receiverAddress: string
  receiverPhone: string
  rxType: number
  rxId: number
  rxName: string
  status: number
  logisticsType: number
  logisticsNo: string
  logisticsComId: string
  drugs: string
  createTime: string
  drugList: HealthCenterOrderDrugVO[]
}

// 推送订单 VO
export interface HealthCenterOrderPushVO {
  id: number
  orderPrice?: number
  rxType?: number
  rxId?: number
  rxNum?: string
  rxName?: string
}

// 处方药品药店信息 DTO
export interface PrescriptionDrugRespDTO {
  drugId: number
  num: string
  name: string
  inventory: number
  unitPrice: number
}

// 处方药品药店信息响应 DTO
export interface PrescriptionDrugRxRespDTO {
  rxType: number
  rxId: number
  rxNum: string
  rxName: string
  rxAddress: string
  rxProvinceCode: string
  rxProvinceName: string
  rxCityCode: string
  rxCityName: string
  rxDistrictCode: string
  rxDistrictName: string
  totalUnitPrice: number
  checkDrugList: PrescriptionDrugRespDTO[]
}

// 获得卫生院订单分页
export const getHealthCenterOrderPageApi = (params: HealthCenterOrderPageReqVO) => {
  return request.get({ url: '/patient/health-center-order/page', params })
}

// 导出卫生院订单 Excel
export const exportHealthCenterOrderExcelApi = (params: HealthCenterOrderExportReqVO) => {
  return request.download({ url: '/patient/health-center-order/export-excel', params })
}

// 分页获取处理意见药品匹配药店列表
export const getPageSuggestRxApi = (params: {
  suggestId: number
  rxName: string
  pageNo: number
  pageSize: number
}) => {
  return request.get({ url: '/patient/health-center-order/page-suggest-rx', params })
}

// 推送订单
export const pushSuggestOrderApi = (data: HealthCenterOrderPushVO) => {
  return request.post({ url: '/patient/health-center-order/push-suggest-order', data })
} 