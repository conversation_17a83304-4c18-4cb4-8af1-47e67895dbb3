import request from '@/config/axios'

export interface BloodOxygenVO {
  id: number
  gpId: number
  sickId: number
  saturation: number
  pulseRate: number
  checkResult: number
  checkContent: string
  deviceCode: string
  measuringTime: Date
}

export interface BloodOxygenPageReqVO extends PageParam {
  gpId?: number
  sickId?: number
  saturation?: number
  pulseRate?: number
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

export interface BloodOxygenExcelReqVO {
  gpId?: number
  sickId?: number
  saturation?: number
  pulseRate?: number
  checkResult?: number
  checkContent?: string
  deviceCode?: string
  measuringTime?: Date[]
  createTime?: Date[]
}

// 查询血氧健康检查结果列表
export const getBloodOxygenPageApi = async (params: BloodOxygenPageReqVO) => {
  return await request.get({ url: '/patient/blood-oxygen/page', params })
}

// 查询血氧健康检查结果详情
export const getBloodOxygenApi = async (id: number) => {
  return await request.get({ url: '/patient/blood-oxygen/get?id=' + id })
}

// 新增血氧健康检查结果
export const createBloodOxygenApi = async (data: BloodOxygenVO) => {
  return await request.post({ url: '/patient/blood-oxygen/create', data })
}

// 修改血氧健康检查结果
export const updateBloodOxygenApi = async (data: BloodOxygenVO) => {
  return await request.put({ url: '/patient/blood-oxygen/update', data })
}

// 删除血氧健康检查结果
export const deleteBloodOxygenApi = async (id: number) => {
  return await request.delete({ url: '/patient/blood-oxygen/delete?id=' + id })
}

// 导出血氧健康检查结果 Excel
export const exportBloodOxygenApi = async (params: BloodOxygenExcelReqVO) => {
  return await request.download({ url: '/patient/blood-oxygen/export-excel', params })
}
