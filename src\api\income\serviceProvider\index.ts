import request from '@/config/axios'

// 查询服务商列表
export const getServiceProviderPageApi = (params) => {
  return request.get({ url: '/operate/service-provider/page', params })
}

// 新增服务商
export const createServiceProviderApi = (data) => {
  return request.post({ url: '/operate/service-provider/create', data })
}

// 修改参数
export const updateServiceProviderApi = (data) => {
  return request.put({ url: '/operate/service-provider/update', data })
}
