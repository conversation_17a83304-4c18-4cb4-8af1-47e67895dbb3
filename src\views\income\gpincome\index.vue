<template>
  <ContentWrap>
    <!-- 列表 -->
    <vxe-grid ref="xGrid" v-bind="gridOptions" class="xtable-scrollbar">
      <template #gpName>
        <el-input v-model="queryParams.gpName" clearable/>
      </template>
      <template #toolbar_buttons>
        <!-- 操作：新增 -->
        <XButton
          type="primary"
          preIcon="ep:zoom-in"
          :title="t('action.add')"
          v-hasPermi="['income:gp:create']"
          @click="handleCreate()"
        />
        <!-- 操作：导出 -->
        <XButton
          type="warning"
          preIcon="ep:download"
          :title="t('action.export')"
          v-hasPermi="['gp:income-pay:export']"
          @click="handleExport()"
        />
      </template>
      <template #actionbtns_default="{ row }">
        <!-- 操作：修改 -->
        <XTextButton
          preIcon="ep:edit"
          :title="t('action.edit')"
          v-hasPermi="['income:gp:update']"
          @click="handleUpdate(row.id)"
        />
        <!-- 操作：详情 -->
        <XTextButton
          v-show="row.objectType !== 7"
          preIcon="ep:view"
          :title="t('action.detail')"
          v-hasPermi="['gp:income-pay:query']"
          @click="handleDetail(row.id, row)"
        />
        <!-- 操作：删除 -->
        <XTextButton
          preIcon="ep:delete"
          :title="t('action.del')"
          v-hasPermi="['income:gp:delete']"
          @click="handleDelete(row.id)"
        />
      </template>

      <template #objectType_template="{ row }">
        <DictTag :type="DICT_TYPE.GP_INCOME_TYPE" :value="row?.objectType" />
      </template>
    </vxe-grid>
  </ContentWrap>
  <!-- 弹窗 -->
  <XModal id="incomePayModel" :loading="modelLoading" v-model="modelVisible" :title="modelTitle">
    <!-- 表单：添加/修改 -->
    <Form
      ref="formRef"
      v-if="['create', 'update'].includes(actionType)"
      :schema="allSchemas.formSchema"
      :rules="rules"
    />
    <!-- 表单：详情 -->
    <!-- <Descriptions
      v-if="actionType === 'detail'"
      :schema="allSchemas.detailSchema"
      :data="detailData"
    /> -->
    <TaskDetail
      :rowId="t_rowid"
      :objectId="t_objectid"
      :userType="1"
      :objectType="t_objecttype"
      v-if="actionType === 'detail'"
    />
    <TaskCheckDetail
      :t_gpName="t_gpName"
      :rowId="t_rowid"
      :objectId="t_objectid"
      v-if="actionType === 'checkDetail'"
    />

    <template #footer>
      <!-- 按钮：保存 -->
      <XButton
        v-if="['create', 'update'].includes(actionType)"
        type="primary"
        :title="t('action.save')"
        :loading="actionLoading"
        @click="submitForm()"
      />
      <!-- 按钮：关闭 -->
      <XButton :loading="actionLoading" :title="t('dialog.close')" @click="modelVisible = false" />
    </template>
  </XModal>
</template>
<script setup lang="ts" name="IncomePay">
// 全局相关的 import
import { ref, unref } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useVxeGrid } from '@/hooks/web/useVxeGrid'
import { VxeGridInstance } from 'vxe-table'
import { FormExpose } from '@/components/Form'
// 业务相关的 import
import { rules, allSchemas } from './incomePay.data'
import * as IncomePayApi from '@/api/income/gpincome'

import { DICT_TYPE } from '@/utils/dict'
// @ts-ignore
import TaskDetail from '@/views/income/tasklog/detail/index.vue'
// @ts-ignore
import TaskCheckDetail from '@/views/income/tasklog/checkdetail/index.vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const { query } = useRoute() // 查询参数
const queryParams = reactive({...query})

// 列表相关的变量
const xGrid = ref<VxeGridInstance>() // 列表 Grid Ref
const { gridOptions, getList, deleteData, getSearchData } = useVxeGrid<IncomePayApi.IncomePayVO>({
  allSchemas: allSchemas,
  getListApi: IncomePayApi.getIncomePayPageApi,
  deleteApi: IncomePayApi.deleteIncomePayApi,
  queryParams: queryParams,
})

// 弹窗相关的变量
const modelVisible = ref(false) // 是否显示弹出层
const modelTitle = ref('edit') // 弹出层标题
const modelLoading = ref(false) // 弹出层loading
const actionType = ref('') // 操作按钮的类型
const actionLoading = ref(false) // 按钮 Loading
const formRef = ref<FormExpose>() // 表单 Ref
// const detailData = ref() // 详情 Ref
const t_rowid = ref(0) //传参
const t_objectid = ref(0)
const t_objecttype = ref(0)
let t_gpName = ref('')

// 设置标题
const setDialogTile = (type: string) => {
  modelLoading.value = true
  modelTitle.value = t('action.' + type)
  actionType.value = type
  modelVisible.value = true
}

// 新增操作
const handleCreate = () => {
  setDialogTile('create')
  modelLoading.value = false
}

// 导出操作
const handleExport = async () => {
  try {
    const searchParams = await getSearchData(xGrid)
    const res = await IncomePayApi.exportIncomePayAsyncApi(searchParams)
    console.log(res)
    message.success('导出请求已提交，请稍后查看基础设施-导出列表')
  }
  catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

// 修改操作
const handleUpdate = async (rowId: number) => {
  setDialogTile('update')
  // 设置数据
  const res = await IncomePayApi.getIncomePayApi(rowId)
  unref(formRef)?.setValues(res)
  modelLoading.value = false
}

// 详情操作
const handleDetail = async (rowId: number, row: any) => {
  const res = await IncomePayApi.getIncomePayApi(rowId)
  console.log(row)
  t_gpName.value = row.gpName
  t_rowid.value = rowId
  t_objectid.value = res.objectId
  t_objecttype.value = res.objectType
  console.log('t_rowid--' + t_rowid.value)
  console.log('t_objectid--' + t_objectid.value)
  if (res.objectId === 4) {
    setDialogTile('checkDetail')
    modelTitle.value = '健康检查'
  } else {
    setDialogTile('detail')
  }

  // detailData.value = res
  modelLoading.value = false
}

// 删除操作
const handleDelete = async (rowId: number) => {
  await deleteData(xGrid, rowId)
}

// 提交按钮
const submitForm = async () => {
  const elForm = unref(formRef)?.getElFormRef()
  if (!elForm) return
  elForm.validate(async (valid) => {
    if (valid) {
      actionLoading.value = true
      // 提交请求
      try {
        const data = unref(formRef)?.formModel as IncomePayApi.IncomePayVO
        if (actionType.value === 'create') {
          await IncomePayApi.createIncomePayApi(data)
          message.success(t('common.createSuccess'))
        } else {
          await IncomePayApi.updateIncomePayApi(data)
          message.success(t('common.updateSuccess'))
        }
        modelVisible.value = false
      } finally {
        actionLoading.value = false
        // 刷新列表
        await getList(xGrid)
      }
    }
  })
}
</script>
