export type SickRelationVO = {
  id: number
  patientId: number
  sickId: number
  relationType: number
  isDefault: number
  status: number
  bindTime: string
}

export type SickRelationPageReqVO = {
  patientId: number
  sickId: number
  relationType: number
  isDefault: number
  status: number
  bindTime: string
  createTime: string
}

export type SickRelationExcelReqVO = {
  patientId: number
  sickId: number
  relationType: number
  isDefault: number
  status: number
  bindTime: string
  createTime: string
}