<template>
  <div
:class="[
    'bg-[var(--el-color-white)] dark:(bg-[var(--el-bg-color)] border-[var(--el-border-color)] border-1px)'
  ]" style="height: 800px">
    <el-descriptions title="会诊信息" direction="vertical" :column="4" border>
      <el-descriptions-item label="会诊ID">{{ detailDataObj?.id }}</el-descriptions-item>
      <el-descriptions-item label="会诊类型">{{ detailDataObj?.type === 1 ? '村医会诊' : '卫生院会诊' }}</el-descriptions-item>
      <el-descriptions-item label="会诊状态">
        {{ detailDataObj?.visitStatus }}
      </el-descriptions-item>
      <el-descriptions-item label="是否开具处方">
        {{ detailDataObj?.isPrescription }}
      </el-descriptions-item>
      <el-descriptions-item label="是否开具处理意见">
        {{ detailDataObj?.isSuggest }}
      </el-descriptions-item>
      <el-descriptions-item label="会诊时长(秒)">
        {{ detailDataObj?.duration }}
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions title="患者信息" direction="vertical" :column="3" border>
      <el-descriptions-item label="患者姓名">{{ detailDataObj?.sickName }}</el-descriptions-item>
      <el-descriptions-item label="手机号">{{ detailDataObj?.sickPhone }}</el-descriptions-item>
      <el-descriptions-item label="性别">{{ detailDataObj?.sickSex }}</el-descriptions-item>
      <el-descriptions-item label="年龄">{{ detailDataObj?.sickAge }}</el-descriptions-item>
      <el-descriptions-item label="出生日期">{{
        detailDataObj?.sickBirthday
      }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions v-if="detailDataObj?.gpInfo" title="发起医生" direction="vertical" :column="5" border>
      <el-descriptions-item label="头像">
        <ElImage :src="detailDataObj.gpInfo?.headImg" style="width: 40px; height: 40px" />
      </el-descriptions-item>
      <el-descriptions-item label="姓名">{{ detailDataObj.gpInfo?.gpName }}</el-descriptions-item>
      <el-descriptions-item label="所属医院">{{
        detailDataObj.gpInfo?.hospitalName
      }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions v-if="detailDataObj?.doctorHospitalName" title="会诊专家" direction="vertical" :column="5" border>
      <el-descriptions-item label="头像">
        <ElImage :src="detailDataObj?.doctorHeadImg" style="width: 40px; height: 40px" />
      </el-descriptions-item>
      <el-descriptions-item label="姓名">{{ detailDataObj?.doctorName }}</el-descriptions-item>
      <el-descriptions-item label="职称">{{ detailDataObj?.doctorRankName }}</el-descriptions-item>
      <el-descriptions-item label="医院">{{
        detailDataObj?.doctorHospitalName
      }}</el-descriptions-item>
      <el-descriptions-item label="科室">{{
        detailDataObj?.hospitalDepart
      }}</el-descriptions-item>
    </el-descriptions>


    <el-descriptions title="免费问诊信息" direction="vertical" :column="6" border v-if="detailDataObj?.visitObj">
      <el-descriptions-item label="订单编号">
        {{ detailDataObj?.visitObj?.orderNum }}
      </el-descriptions-item>
      <el-descriptions-item label="接诊医师">{{
        detailDataObj.visitObj?.doctorName
      }}</el-descriptions-item>
      <el-descriptions-item label="患者姓名">{{
        detailDataObj?.visitObj?.patientName
      }}</el-descriptions-item>
      <el-descriptions-item label="问诊状态">
        <DictTag :type="DICT_TYPE.PATIENT_VISIT_STATUS" :value="detailDataObj.visitObj?.status" />
      </el-descriptions-item>
      <el-descriptions-item label="是否开处方">{{
        detailDataObj.visitObj?.isPrescription
      }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{
        ge_time_format(detailDataObj.visitObj?.createTime)
      }}</el-descriptions-item>
    </el-descriptions>

    <el-descriptions v-if="detailDataObj?.type === 1" title="病历资料" direction="vertical" :column="1" border>
      <el-descriptions-item label="病历资料">
        <div style="display: flex; flex-wrap: wrap; gap: 10px;">
          <el-image v-for="item in detailDataObj?.mrMaterialList" :key="item" :preview-src-list="detailDataObj?.mrMaterialList" :src="item" style="width: 88px; height: 88px;"/>
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions v-if="detailDataObj?.type === 2" title="会诊病历/材料" direction="vertical" :column="1" border>
      <el-descriptions-item label="会诊病历/材料">
        <el-button type="text" @click="showEmrDialog">查看详情</el-button>
      </el-descriptions-item>
    </el-descriptions>
    
    <el-descriptions title="处方" direction="vertical" :column="1" border v-if="detailDataObj?.telemedicineInfo?.prescriptionPdf">
      <el-descriptions-item label="处方">
        <a :href="detailDataObj?.telemedicineInfo?.prescriptionPdf" target="_blank" style="color: #409EFF;">查看详情</a>
      </el-descriptions-item>
    </el-descriptions>

    <el-descriptions
v-if="detailDataObj?.diagnoseSuggestList && detailDataObj?.diagnoseSuggestList?.length > 0"
      title="处理意见" direction="horizontal" :column="1" border class="doctorList" />

    <el-descriptions
v-if="detailDataObj?.diagnoseSuggestList && detailDataObj?.diagnoseSuggestList?.length > 0"
      direction="horizontal" :column="1" border>
      <template v-for="item in detailDataObj.diagnoseSuggestList" :key="item">
        <el-descriptions-item label="西医诊断意见">{{ item.content }} </el-descriptions-item>
        <el-descriptions-item label="中医诊断意见">{{ item.tcmDiagnosis }} </el-descriptions-item>
        <el-descriptions-item v-if="item.type === 2" label="中医证型">{{ item.syndromeType }} </el-descriptions-item>
        <el-descriptions-item label="处理意见"> {{ item.initialDiagnosis }}</el-descriptions-item>
        <el-descriptions-item v-if="item.tcmDrug.length > 0" label="中药建议">
          <div style="display: flex; flex-wrap: wrap;">
            <p v-for="drug in item.tcmDrug" :key="drug.drugName" class="drug_name" style="width: 25%;margin-bottom: 10px;">
              <span v-if="drug.prepareType" style="position: relative;margin-right: 20px;">
                {{ drug.drugName }}
                <span style="font-size: 10px; color: rgba(0,0,0,0.85);position: absolute; top: -10px; right: -18px;">{{ drug.prepareType }}</span>
              </span>
              <span v-else>{{ drug.drugName }}</span>
              <span style="margin: 0 20px 0 0;">{{ drug.weight }}g</span>
            </p>
          </div>
          <div class="drug_specification">剂数： {{ item.dosesNum }}剂 (每剂分{{item.takenTimes}}次服用)</div>
          <div class="drug_specification">用法： {{ item.usage === 1? '内服' : '外用' }}</div>
          <div class="drug_specification">加工方式： {{ item.processingMethod }}</div>
          <div class="drug_specification">使用方法： {{ item.usageMethod }}</div>
          <div class="drug_specification">注意事项： {{ item.note }}</div>
        </el-descriptions-item>
        <el-descriptions-item v-if="item.redDrug.length > 0" label="西药建议">
          <el-table :data="item.redDrug" border>
            <el-table-column fixed prop="drugName" label="药品" />
            <el-table-column prop="drugTypeName" label="药品分类" />
            <el-table-column prop="size" label="规格" />
            <el-table-column prop="frequencyText" label="用药频率" />
            <el-table-column label="单次用量" >
              <template #default="{row}">
                {{ row.quantityText }} {{ row.dosageUnitText }}
              </template>
            </el-table-column>
            <el-table-column prop="methodText" label="用药方式" />
            <el-table-column prop="count" label="数量" />
          </el-table>
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <el-descriptions title="视频记录" direction="vertical" :column="1" border>
      <el-descriptions-item label="视频">
        <a
          download
          :href="url.url"
          target="_blank"
          v-for="url in media.video"
          :key="url.url"
          style="display: block; margin-bottom: 10px; color: rgba(59, 130, 246, 1)"
        >
          视频 - {{ url.name }}
        </a>
      </el-descriptions-item>
    </el-descriptions>
    <el-descriptions
v-if="detailDataObj?.appointmentVisitObj && detailDataObj?.appointmentVisitObj.length > 0"
      title="预约单" direction="vertical" :column="5" class="descriptionsHideLabel" border>
      <template v-for="item in detailDataObj.appointmentVisitObj" :key="item">
        <el-descriptions-item label="预约单号">{{ item.appointmentCode }}</el-descriptions-item>
        <el-descriptions-item label="预约医生">
          {{ item.doctorName }} {{ item.doctorRankName }}
        </el-descriptions-item>
        <el-descriptions-item label="就诊人">
          {{ item.sickName }} {{ item.sickSex }} {{ monthToAge(item.sickAge) }}
        </el-descriptions-item>
        <el-descriptions-item label="预约开始时间">
          {{ ge_time_format(item.startTime, '5') }}
        </el-descriptions-item>
        <el-descriptions-item label="预约结束时间">
          {{ ge_time_format(item.expireTime, '5') }}
        </el-descriptions-item>
      </template>
    </el-descriptions>

    <el-descriptions
v-if="detailDataObj?.followVisitList && detailDataObj?.followVisitList.length > 0" title="随访记录"
      direction="vertical" :column="5" class="descriptionsHideLabel" border>
      <template v-for="item in detailDataObj.followVisitList" :key="item">
        <el-descriptions-item label="随访时间">{{
         item.visitTime ?dayjs(item.visitTime).format('YYYY-MM-DD'):"--"
        }}</el-descriptions-item>
        <el-descriptions-item label="治疗效果">
          {{ item.therapeuticEffect }}
        </el-descriptions-item>
        <el-descriptions-item label="用药依从性">
          {{ item.compliance }}
        </el-descriptions-item>
        <el-descriptions-item label="用药不良反应">
          {{ item.adverseReaction }}
        </el-descriptions-item>
        <el-descriptions-item label="其他说明">
          {{ item.content }}
        </el-descriptions-item>
      </template>
    </el-descriptions>
    <el-descriptions title="患者评价" direction="vertical" :column="3" border v-if="evaluate.data.createTime">
      <el-descriptions-item label="评价星级">
        <el-rate :model-value="evaluate.data.level" disabled />
      </el-descriptions-item>
      <el-descriptions-item label="评价">{{ evaluate.data.content }}</el-descriptions-item>
      <el-descriptions-item label="时间">{{
        formatDate(evaluate.data.createTime)
      }}</el-descriptions-item>
    </el-descriptions>

    <!-- 会诊病历详情弹窗 -->
    <el-dialog v-model="emrDialogVisible" title="会诊病历" width="80%" destroy-on-close>
      <div v-if="detailDataObj?.emr">
        <el-descriptions direction="vertical" :column="5" border>
          <el-descriptions-item label="姓名">{{ detailDataObj.emr.sickName || '--' }}</el-descriptions-item>
          <el-descriptions-item label="性别">{{ detailDataObj.emr.sickSex === 1 ? '男' : detailDataObj.emr.sickSex === 2 ? '女' : '--' }}</el-descriptions-item>
          <el-descriptions-item label="年龄">{{ detailDataObj.emr.sickAge ? monthToAge(detailDataObj.emr.sickAge) : '--' }}</el-descriptions-item>
        </el-descriptions>

        <el-descriptions direction="vertical" :column="1" border>
          <el-descriptions-item label="主诉">
            <div style="white-space: pre-wrap;">{{ detailDataObj.emr.principleAction || '--' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="现病史">
            <div style="white-space: pre-wrap;">{{ detailDataObj.emr.currentIllness || '--' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="既往史">
            <div style="white-space: pre-wrap;">{{ detailDataObj.emr.personIllness || '--' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="过敏史">
            <div style="white-space: pre-wrap;">{{ detailDataObj.emr.allergyIllness || '--' }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions direction="vertical" :column="1" border>
          <el-descriptions-item label="辅助信息">
            <div v-if="detailDataObj.emr.assistImgUrl && detailDataObj.emr.assistImgUrl.length > 0" style="display: flex; flex-wrap: wrap; gap: 10px;">
              <el-image 
                v-for="(item, index) in detailDataObj.emr.assistImgUrl" 
                :key="index" 
                :src="item" 
                :preview-src-list="detailDataObj.emr.assistImgUrl" 
                style="width: 100px; height: 100px; border-radius: 4px;"
                fit="cover"
              />
            </div>
            <span v-else>暂无</span>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions direction="vertical" :column="1" border>
          <el-descriptions-item label="西医诊断">
            <div v-if="detailDataObj.emr.initialDiagnosisList && detailDataObj.emr.initialDiagnosisList.length > 0">
              <span v-for="(item, index) in detailDataObj.emr.initialDiagnosisList" :key="item.id">
                {{ item.name }}<span v-if="index < detailDataObj.emr.initialDiagnosisList.length - 1">, </span>
              </span>
            </div>
            <span v-else>{{ detailDataObj.emr.initialDiagnosis || '--' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="中医诊断">
            <div v-if="detailDataObj.emr.tcmDiagnosisList && detailDataObj.emr.tcmDiagnosisList.length > 0">
              <span v-for="(item, index) in detailDataObj.emr.tcmDiagnosisList" :key="item.id">
                {{ item.name }}<span v-if="index < detailDataObj.emr.tcmDiagnosisList.length - 1">, </span>
              </span>
            </div>
            <span v-else>{{ detailDataObj.emr.tcmDiagnosis || '--' }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="中医证型">
            <div style="white-space: pre-wrap;">{{ detailDataObj.emr.syndromeType || '--' }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions direction="vertical" :column="1" border>
          <el-descriptions-item label="处置方案">
            <div style="white-space: pre-wrap;">{{ detailDataObj.emr.suggestContent || '--' }}</div>
          </el-descriptions-item>
        </el-descriptions>

        <el-descriptions direction="vertical" :column="1" border v-if="detailDataObj?.diagnoseSuggestList && detailDataObj.diagnoseSuggestList.length > 0">
          <template v-for="item in detailDataObj.diagnoseSuggestList" :key="item">
            <el-descriptions-item v-if="item.redDrug && item.redDrug.length > 0" label="西药方案">
              <el-table :data="item.redDrug" border>
                <el-table-column fixed prop="name" label="药品" />
                <el-table-column prop="drugTypeName" label="药品分类" />
                <el-table-column prop="specification" label="规格" />
                <el-table-column prop="frequencyText" label="用药频率" />
                <el-table-column label="单次用量" >
                  <template #default="{row}">
                    {{ row.quantityText }} {{ row.dosageUnitText }}
                  </template>
                </el-table-column>
                <el-table-column prop="methodText" label="用药方式" />
                <el-table-column prop="count" label="开药数量(盒)" />
              </el-table>
            </el-descriptions-item>
          </template>
        </el-descriptions>

        <el-descriptions direction="vertical" :column="1" border v-if="detailDataObj?.diagnoseSuggestList && detailDataObj.diagnoseSuggestList.length > 0">
          <template v-for="item in detailDataObj.diagnoseSuggestList" :key="item">
            <el-descriptions-item v-if="item.tcmDrug && item.tcmDrug.length > 0" label="中药方案">
              <div style="display: flex; flex-wrap: wrap;">
                <p v-for="drug in item.tcmDrug" :key="drug.name" class="drug_name" style="width: 25%;margin-bottom: 10px;">
                  <span v-if="drug.prepareType" style="position: relative;margin-right: 20px;">
                    {{ drug.name }}
                    <span style="font-size: 10px; color: rgba(0,0,0,0.85);position: absolute; top: -10px; right: -18px;">{{ drug.prepareType }}</span>
                  </span>
                  <span v-else>{{ drug.name }}</span>
                  <span style="margin: 0 20px 0 0;">{{ drug.weight }}g</span>
                </p>
              </div>
              <div class="drug_specification">剂数： {{ item.dosesNum }}剂 (每剂分{{item.takenTimes}}次服用)</div>
              <div class="drug_specification">用法： {{ item.usage === 1? '内服' : '外用' }}</div>
              <div class="drug_specification">加工方式： {{ item.processingMethod }}</div>
              <div class="drug_specification">使用方法： {{ item.usageMethod }}</div>
              <div class="drug_specification">注意事项： {{ item.note }}</div>
            </el-descriptions-item>
          </template>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>
<script setup lang="ts">
//@ts-ignore
import dayjs from 'dayjs'
import { ge_time_format, monthToAge } from '@/utils/custom'
import { getDataObject } from './index'
import { DICT_TYPE } from '@/utils/dict'
import { getEvaluatesByVisitTypeApi } from '@/api/visit/appointment'
const props = defineProps({
  id: {
    type: Number,
    required: true
  },
})
// 在你的组件类中定义 formatDate 函数
function formatDate(timestamp: number): string {
  const date = new Date(timestamp) // 不需要乘以1000，因为已经是毫秒级的时间戳
  const formattedDate = date.toLocaleString() // 使用默认的日期时间格式
  return formattedDate
}
let evaluate = reactive({
  data: {
    level: 0,
    content: '',
    createTime: 0
  }
})
const detailDataObj = ref<any>({})
const emrDialogVisible = ref(false) // 病历弹窗显示状态
const media: Ref<{
  audio: any[]
  video: any[]
}> = ref({
  audio: [],
  video: []
})

// 显示病历详情弹窗
const showEmrDialog = () => {
  emrDialogVisible.value = true
}
onMounted(async () => {
  detailDataObj.value = await getDataObject(props.id);
  media.value.audio = []
  media.value.video = []
  media.value.audio = detailDataObj.value.audioRecordList
  media.value.video = detailDataObj.value.videoRecordList
  let res = await getEvaluatesByVisitTypeApi(detailDataObj.value?.id, 4);//4为一对一
  if (res) {
    let obj = {
      level: res.level,
      content: res.content,
      createTime: res.evaluateTimestamp
    }
    evaluate.data = obj
  }
})
</script>

<style lang="less" scoped>
.el-descriptions {
  margin-top: 20px;
}

.cell-item {
  display: flex;
  align-items: center;
}

.margin-top {
  margin-top: 20px;
}

.doctorList {
  :deep(.el-descriptions__header) {
    margin: 0;
  }
}

.descriptionsHideLabel {
  :deep(table tbody tr:not(:first-child):nth-child(odd)) {
    display: none;
  }
}
</style>
