import request from '@/config/axios'

export interface ShopVO {
  id: number
  num: string
  name: string
  provinceCode: string
  cityCode: string
  districtCode: string
  address: string
  taxNum: string
  sort: number
}

export interface ShopPageReqVO extends PageParam {
  num?: string
  name?: string
  provinceCode?: string
  cityCode?: string
  districtCode?: string
  address?: string
  taxNum?: string
  sort?: number
  createTime?: Date[]
}

export interface ShopExcelReqVO {
  num?: string
  name?: string
  provinceCode?: string
  cityCode?: string
  districtCode?: string
  address?: string
  taxNum?: string
  sort?: number
  createTime?: Date[]
}

// 查询门店信息管理列表
export const getShopPageApi = async (params: ShopPageReqVO) => {
  return await request.get({ url: '/operate/shop/page', params })
}

// 查询门店信息管理详情
export const getShopApi = async (id: number) => {
  return await request.get({ url: '/operate/shop/get?id=' + id })
}

// 新增门店信息管理
export const createShopApi = async (data: ShopVO) => {
  return await request.post({ url: '/operate/shop/create', data })
}

// 修改门店信息管理
export const updateShopApi = async (data: ShopVO) => {
  return await request.put({ url: '/operate/shop/update', data })
}

// 删除门店信息管理
export const deleteShopApi = async (id: number) => {
  return await request.delete({ url: '/operate/shop/delete?id=' + id })
}

// 导出门店信息管理 Excel
export const exportShopApi = async (params: ShopExcelReqVO) => {
  return await request.download({ url: '/operate/shop/export-excel', params })
}
