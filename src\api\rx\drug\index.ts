import request from '@/config/axios'

export interface DrugVO {
  id: number
  drugId: number
  drugCode: string
  drugPrice: number
  stock: number
  status: number
  createUserId: number
  drugstoreId: number
}

export interface DrugPageReqVO extends PageParam {
  drugId?: number
  drugCode?: string
  drugPrice?: number
  stock?: number
  status?: number
  createUserId?: number
  createTime?: Date[]
  drugstoreId?: number
}

export interface DrugExcelReqVO {
  drugId?: number
  drugCode?: string
  drugPrice?: number
  stock?: number
  status?: number
  createUserId?: number
  createTime?: Date[]
  drugstoreId?: number
}

// 查询药店药品管理列表
export const getDrugPageApi = async (params: DrugPageReqVO) => {
  return await request.get({ url: '/rx/drug/page', params })
}

// 查询药店药品管理详情
export const getDrugApi = async (id: number) => {
  return await request.get({ url: '/rx/drug/get?id=' + id })
}

// 新增药店药品管理
export const createDrugApi = async (data: DrugVO) => {
  return await request.post({ url: '/rx/drug/create', data })
}

// 修改药店药品管理
export const updateDrugApi = async (data: DrugVO) => {
  return await request.put({ url: '/rx/drug/update', data })
}

// 删除药店药品管理
export const deleteDrugApi = async (id: number) => {
  return await request.delete({ url: '/rx/drug/delete?id=' + id })
}

// 导出药店药品管理 Excel
export const exportDrugApi = async (params: DrugExcelReqVO) => {
  return await request.download({ url: '/rx/drug/export-excel', params })
}


// 获得药品分润列表
export const getRxDrugByDrugIds = async (ids: number[]) => {
  return await request.get({ url: '/rx/drug/list-by-drug?ids=' + ids })
}


