import request from '@/config/axios'

export interface DepartVO {
  id: number
  name: string
  parentId: number
  hospitalId: number
  relationId: number
  iconUrl: string
  sort: number
  about: string
}

export interface DepartPageReqVO extends PageParam {
  name?: string
  hospitalId?: number
}

export interface DepartExcelReqVO {
  name?: string
  hospitalId?: number
}

// 查询医院科室信息列表
export const getDepartPageApi = async (params: DepartPageReqVO) => {
  return await request.get({ url: '/operate/depart/page', params })
}

// 查询医院科室信息详情
export const getDepartApi = async (id: number) => {
  return await request.get({ url: '/operate/depart/get?id=' + id })
}

// 新增医院科室信息
export const createDepartApi = async (data: DepartVO) => {
  return await request.post({ url: '/operate/depart/create', data })
}

// 修改医院科室信息
export const updateDepartApi = async (data: DepartVO) => {
  return await request.put({ url: '/operate/depart/update', data })
}

// 删除医院科室信息
export const deleteDepartApi = async (id: number) => {
  return await request.delete({ url: '/operate/depart/delete?id=' + id })
}

// 导出医院科室信息 Excel
export const exportDepartApi = async (params: DepartExcelReqVO) => {
  return await request.download({ url: '/operate/depart/export-excel', params })
}
// 查询医院科室所有信息
export const getDepartAllApi = async () => {
  const data = await request.get({ url: '/operate/depart/find-depart-list' })
  return data
}
