import request from '@/config/axios'

// 查询特邀会诊任务列表
export const getInvitedExpertTaskPageApi = async (params) => {
  return await request.get({ url: '/doctor/invited-expert-task/page', params })
}

// 导出特邀会诊任务列表
export const exportInvitedExpertTaskPageApi = async (params) => {
  return await request.download({ url: '/doctor/invited-expert-task/export-excel', params })
}

// 创建特邀会诊任务
export const createInvitedExpertTaskApi = async (data) => {
  return await request.post({ url: '/doctor/invited-expert-task/create', data })
}

// 更新特邀会诊任务
export const updateInvitedExpertTaskApi = async (data) => {
  return await request.put({ url: '/doctor/invited-expert-task/update', data })
}

// 删除特邀会诊任务
export const deleteInvitedExpertTaskApi = async (id: number) => {
  return await request.delete({ url: '/doctor/invited-expert-task/delete?id=' + id })
}

// 获取特邀会诊任务详情
export const detailInvitedExpertTaskApi = async (params) => {
  return await request.download({ url: '/doctor/invited-expert-task/get', params })
}
