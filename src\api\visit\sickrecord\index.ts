import request from '@/config/axios'

export interface SickRecordVO {
  id: number
  allergicHistory: string
  familyHistory: string
  sickId: number
  height: number
  weight: number
  bloodType: number
  rhBloodType: number
  previousHistory: string
  exposureHistory: string
  geneticHistory: string
  disabilitySituation: string
}

export interface SickRecordPageReqVO extends PageParam {
  sickId?: number
  height?: number
  weight?: number
  bloodType?: number
  rhBloodType?: number
  previousHistory?: string
  exposureHistory?: string
  geneticHistory?: string
  disabilitySituation?: string
}

export interface SickRecordExcelReqVO {
  sickId?: number
  height?: number
  weight?: number
  bloodType?: number
  rhBloodType?: number
  previousHistory?: string
  exposureHistory?: string
  geneticHistory?: string
  disabilitySituation?: string
}

// 查询患者档案信息列表
export const getSickRecordPageApi = async (params: SickRecordPageReqVO) => {
  return await request.get({ url: '/patient/sick-record/page', params })
}

// 查询患者档案信息详情
export const getSickRecordApi = async (id: number) => {
  return await request.get({ url: '/patient/sick-record/get?id=' + id })
}

// 新增患者档案信息
export const createSickRecordApi = async (data: SickRecordVO) => {
  return await request.post({ url: '/patient/sick-record/create', data })
}

// 修改患者档案信息
export const updateSickRecordApi = async (data: SickRecordVO) => {
  return await request.put({ url: '/patient/sick-record/update', data })
}

// 删除患者档案信息
export const deleteSickRecordApi = async (id: number) => {
  return await request.delete({ url: '/patient/sick-record/delete?id=' + id })
}

// 导出患者档案信息 Excel
export const exportSickRecordApi = async (params: SickRecordExcelReqVO) => {
  return await request.download({ url: '/patient/sick-record/export-excel', params })
}
