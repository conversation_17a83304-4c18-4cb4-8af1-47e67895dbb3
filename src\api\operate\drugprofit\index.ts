import request from '@/config/axios'

export interface DrugProfitVO {
  id: number
  drugId: number
  awardType: number
  status: number
  settlementIntervalStart: number
  settlementIntervalEnd: number
  jobTime: string
  userType: number
  rate: number
  user_ids: string
  missionId: number
  startTime: Date
  endTime: Date
}

export interface DrugProfitPageReqVO extends PageParam {
  drugId?: number
  awardType?: number
  status?: number
  settlementIntervalStart?: number
  settlementIntervalEnd?: number
  jobTime?: string
  createTime?: Date[]
  userType?: number
  rate?: number
  userIds?: string
  missionId?: number
  startTime?: Date[]
  endTime?: Date[]
}

export interface DrugProfitExcelReqVO {
  drugId?: number
  awardType?: number
  status?: number
  settlementIntervalStart?: number
  settlementIntervalEnd?: number
  jobTime?: string
  createTime?: Date[]
  userType?: number
  rate?: number
  userIds?: string
  missionId?: number
  startTime?: Date[]
  endTime?: Date[]
}
export interface DrugProfitExcelReqVO {
  drugId?: number
  awardType?: number
  status?: number
  settlementIntervalStart?: number
  settlementIntervalEnd?: number
  jobTime?: string
  createTime?: Date[]
  userType?: number
  rate?: number
}

// 查询药品分润列表
export const getDrugProfitPageApi = async (params: DrugProfitPageReqVO) => {
  return await request.get({ url: '/operate/drug-profit/page', params })
}

// 查询药品分润详情
export const getDrugProfitApi = async (id: number) => {
  return await request.get({ url: '/operate/drug-profit/get?id=' + id })
}

// 新增药品分润
export const createDrugProfitApi = async (data: DrugProfitVO) => {
  return await request.post({ url: '/operate/drug-profit/create', data })
}

// 修改药品分润
export const updateDrugProfitApi = async (data: DrugProfitVO) => {
  return await request.put({ url: '/operate/drug-profit/update', data })
}

// 删除药品分润
export const deleteDrugProfitApi = async (id: number) => {
  return await request.delete({ url: '/operate/drug-profit/delete?id=' + id })
}

// 导出药品分润 Excel
export const exportDrugProfitApi = async (params: DrugProfitExcelReqVO) => {
  return await request.download({ url: '/operate/drug-profit/export-excel', params })
}

// 获得药品分润列表
export const getDrugProfitListApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/drug-profit/list?ids=' + ids })
}

// 查询药品分润列表-药品唯一
export const getDrugProfitDrugPageApi = async (params: DrugProfitPageReqVO) => {
  return await request.get({ url: '/operate/drug-profit/page-drug', params })
}

// 获得药品分润列表
export const getDrugProfitByDrugApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/drug-profit/list-by-drug?ids=' + ids })
}

// 删除药品分润
export const deleteDrugProfitNewApi = async (data: DrugProfitVO) => {
  return await request.put({ url: '/operate/drug-profit/delete-drug-profit', data })
}
