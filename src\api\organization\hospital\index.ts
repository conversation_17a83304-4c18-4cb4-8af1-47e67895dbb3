import request from '@/config/axios'

export interface HospitalVO {
  id: number
  sort: number
  name: string
  typeId: number
  nature: number
  rankLevel: number
  about: string
  provinceCode: number
  cityCode: number
  districtCode: number
  streetCode: number
  code: string
  shortName: string
  address: string
  regNum: string
  socialCode: string
  hospitalType: number
  phone: string
  longitude: object
  latitude: object
}

export interface HospitalPageReqVO extends PageParam {
  createTime?: Date[]
  name?: string
  typeId?: number
  nature?: number
  rankLevel?: number
  about?: string
  provinceCode?: number
  cityCode?: number
  districtCode?: number
  code?: string
  shortName?: string
  address?: string
  regNum?: string
  socialCode?: string
  hospitalType?: number
  phone?: string
  longitude?: object
  latitude?: object
  streetCode?: number
}

export interface HospitalExcelReqVO {
  createTime?: Date[]
  name?: string
  typeId?: number
  nature?: number
  rankLevel?: number
  about?: string
  provinceCode?: number
  cityCode?: number
  districtCode?: number
  code?: string
  shortName?: string
  address?: string
  regNum?: string
  socialCode?: string
  hospitalType?: number
  phone?: string
  longitude?: object
  latitude?: object
  streetCode?: number
}

// 查询医院信息列表
export const getHospitalPageApi = async (params: HospitalPageReqVO) => {
  return await request.get({ url: '/operate/hospital/page', params })
}

// 查询医院信息详情
export const getHospitalApi = async (id: number) => {
  return await request.get({ url: '/operate/hospital/get?id=' + id })
}

export const getHospitalListApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/hospital/list?ids=' + ids })
}

// 新增医院信息
export const createHospitalApi = async (data: HospitalVO) => {
  return await request.post({ url: '/operate/hospital/create', data })
}

// 获取导入模板
export const getImportTemplateApi = async () => {
  return await request.download({ url: '/operate/hospital/get-import-template' })
}

// 导入医院信息
export const importHospitalApi = async (data) => {
  return await request.post({ url: '/operate/hospital/import', data })
}

// 修改医院信息
export const updateHospitalApi = async (data: HospitalVO) => {
  return await request.put({ url: '/operate/hospital/update', data })
}

// 删除医院信息
export const deleteHospitalApi = async (id: number) => {
  return await request.delete({ url: '/operate/hospital/delete?id=' + id })
}

// 导出医院信息 Excel
export const exportHospitalApi = async (params: HospitalExcelReqVO) => {
  return await request.download({ url: '/operate/hospital/export-excel', params })
}


export const getHospitalAllApi = async () => {
  return await request.get({ url: '/operate/hospital/find-hospital-list' })
}
