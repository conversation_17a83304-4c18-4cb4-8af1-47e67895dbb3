import request from '@/config/axios'

// 获取慢性病随访列表
export const getChronicFollowVisitPageApi = async (params) => {
  return await request.get({ url: '/patient/chronic-follow-visit/page-follow', params })
}

// 获取慢性病随访列表
export const getChronicFollowVisitDetailApi = async (params) => {
  return await request.get({ url: '/patient/chronic-follow-visit/find-detail-by-id', params })
}

//导出慢性病随访列表
export const exportChronicFollowVisitApi = async (params) => {
  return await request.download({ url: '/patient/chronic-follow-visit/export-excel', params })
}
