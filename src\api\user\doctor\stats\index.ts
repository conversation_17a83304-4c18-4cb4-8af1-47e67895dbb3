import request from '@/config/axios'
import { UserRegisterStatsEChartData } from '@/api/user/doctor/stats/types'
import { ge_time_format } from '@/utils/custom'


// 查询患者注册统计信息
export const getDoctorRegisterDataApi = async (params: any):Promise<UserRegisterStatsEChartData> => {
  const userRegisterStatsApiData=await request.get({ url: '/doctor/user-register-stats/find-user-register-stats?type=' + params+'&pageNo=1&pageSize=10' })
  const userRegisterStatsData: UserRegisterStatsEChartData={
    dataName: [],
    dataValue: []
  }
  for(const item of userRegisterStatsApiData.reverse()){
    const statsStampFormat = params == 1 ? '2' : (params == 2 ? '3' : '11')
      userRegisterStatsData.dataName.push(ge_time_format(item.statsTimeStamp, statsStampFormat))
      userRegisterStatsData.dataValue.push(item.count)
    }
  return userRegisterStatsData
}