export type MessageVO = {
  id: number
  msgOpe: number
  msgFrom: string
  msgTo: string
  msgBody: string
  msgDesc: string
  msgAttach: string
  msgId: number
  msgFromNick: string
  msgType: string
  msgCustomType: string
  msgExt: string
  msgTimestamp: number
  msgDevice: string
  msgClient: string
  msgClientId: string
  msgSid: string
}

export type MessagePageReqVO = {
  msgOpe: number
  msgFrom: string
  msgTo: string
  msgBody: string
  msgDesc: string
  msgAttach: string
  msgId: number
  createTime: string
  msgFromNick: string
  msgType: string
  msgCustomType: string
  msgExt: string
  msgTimestamp: number
  msgDevice: string
  msgClient: string
  msgClientId: string
  msgSid: string
}

export type MessageExcelReqVO = {
  msgOpe: number
  msgFrom: string
  msgTo: string
  msgBody: string
  msgDesc: string
  msgAttach: string
  msgId: number
  createTime: string
  msgFromNick: string
  msgType: string
  msgCustomType: string
  msgExt: string
  msgTimestamp: number
  msgDevice: string
  msgClient: string
  msgClientId: string
  msgSid: string
}