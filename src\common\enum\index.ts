/** 请求状态的枚举
 * @param { number } error 失败
 * @param { number } success 成功
 */
export enum requestStatus {
  error = -1,
  success = 0
}

/** 是否开具处理意见
 * @param { number } notSuggest 否
 * @param { number } isSuggest 是
 */
export enum isSuggestEnum {
  notSuggest = 0,
  isSuggest
}

/** 是否开具处方
 * @param { number } notPrescription 否
 * @param { number } isPrescription 是
 */
export enum visitIsPrescriptionEnum {
  notPrescription = 0,
  isPrescription
}

/** 性别枚举
 * @param {nunber} man 1 男
 * @param {nunber} woman 2 女
 */
export enum sexEnum {
  man = 1,
  woman
}

/** 问诊房间类型（与云信无关）
 * @param { number } ImgText 图文
 * @param { number } audio 音频
 * @param { number } video 视频
 * @param { number } consultation 1对1会诊
 * @param { number } multiConsultation 多方会诊
 */
export enum roomType {
  ImgText = 1,
  audio,
  video,
  consultation,
  multiConsultation
}

// /** 问诊状态
//  * @param { number } ALL 全部订单
//  * @param { number } WAIT 待付款
//  * @param { number } PROGRESS_VISIT 问诊中
//  * @param { number } COMPLETE 已完成
//  * @param { number } CANCEL 已取消
//  */
// export enum visitStatusEnum {
//   ALL = 0,
//   WAIT,
//   PROGRESS_VISIT,
//   COMPLETE,
//   CANCEL
// }

/** 问诊类型
 * @param { number } TEXT 图文问诊
 * @param { number } AUDIO 语音问诊
 * @param { number } VIDEO 视频问诊
 */
export enum visitTypeEnum {
  TEXT = 0,
  AUDIO,
  VIDEO
}

/** operate类型区分
 * @param { number } PATIENTCONTENT 问诊
 * @param { number } MEDICALRECORD 电子病历
 * @param { number } FEEDBACK 意见反馈
 * @param { number } AD 广告
 * @param { number } DRUGIMG 药品图片
 * @param { number } REPORT 举报
 */
export enum operateTypeEnum {
  PATIENTCONTENT = 1,
  MEDICALRECORD,
  FEEDBACK,
  AD,
  DRUGIMG,
  REPORT,
}

/** 药品类型
 * @param { number } Western 西医
 * @param { number } China 中医
 */
export enum operateCwTypeEnum {
  Western = 1,
  China
}
export enum visitStatusEnum {
  Waiting = 0,
  InProgress,
  Completed,
  Expired
}

/** 医生类型
 * @param { number } 医生
 * @param { number } 平台医生
 * @param { number } 村医
 * @param { number } 药师
 */
export enum followDoctorTypeEnum {
  Doctor = 1,
  Telehealth,
  Village,
  Pharmacist,
  InvitedExper
}

/** 任务类型
 * @param { number } drugpeodit 药品分润
 * @param { number } vistivindependent 问诊分账
 * @param { number } taskawaed 任务奖励
 */
export enum awardTypeEnum {
  drugpeodit = 1,
  vistivindependent,
  taskawaed
}

/** 血型
 * @param { number } A A型
 * @param { number } B B型
 * @param { number } AB AB型
 * @param { number } O O型
 * @param { number } Unknown 不详
 */
export enum bloodTypeEnum {
  A = 1,
  B,
  AB,
  O,
  Unknown
}

/** RH血型
 * @param { number } Negative 阴性
 * @param { number } Positive 阳性
 * @param { number } Unknown 不详
 */
export enum rhBloodTypeEnum {
  Negative = 1,
  Positive,
  Unknown
}

/** 身体部位
 * @param { number } left 左手
 * @param { number } right 右手
 */
export enum sickPartEnum {
  left = 1,
  right
}

/** 血氧检查结果
 * @param { number } Unknown 其他
 * @param { number } Normal 结果未见异常
 * @param { number } Abnormal 结果异常
 * @param { number } Anomalous 检测异常
 */
export enum checkResultEnum {
  Unknown = 0,
  Normal,
  Abnormal,
  Anomalous
}
/** 血脂检查结果
 * @param { number } Other 其他
 * @param { number } NoAbnormalities 无异常
 * @param { number } Below 偏低
 * @param { number } Above 偏高
 */
export enum flatCheckResultEnum {
  Other = 0,
  NoAbnormalities,
  Below,
  Above
}

export enum doctorAuthStatusEnum {
  NotAuthenticated = 0,
  Authenticated = 1,
  InProgress = 2,
  Failed = 3
}

/** 血糖检测参考类型
 * @param { number } Fasting 空腹
 * @param { number } PreMeal 餐前
 * @param { number } PostMeal 餐后
 * @param { number } Random 随机
 */
export enum glucoseReferenceTypeEnum {
  Fasting = 1,
  PreMeal,
  PostMeal,
  Random
}

/** 收藏类型
 * @param { number } Monitoring 关注
 * @param { number } Treatment 诊疗
 */
export enum collectTypeEnum {
  Monitoring = 1,
  Treatment
}

/** 收藏类型
 * @param { number } Inquiry 问诊
 * @param { number } ElectronicMedicalRecord 电子病历
 * @param { number } Feedback 意见反馈
 * @param { number } AdPlacement 广告位
 */
export enum imgsTypeEnum {
  Inquiry = 1,
  ElectronicMedicalRecord = 2,
  Feedback = 3,
  AdPlacement = 4
}

/** 参考
 * @param { number } Fasting 空腹
 * @param { number } BeforeMeal 餐前
 * @param { number } AfterMeal 餐后
 */
export enum referenceTypeEnum {
  Fasting = 1,
  BeforeMeal,
  AfterMeal
}
/** 尿酸检查结果
 * @param { number } Unknown 其他
 * @param { number } Normal 结果未见异常
 * @param { number } Abnormal 结果异常
 * @param { number } Anomalous 检测异常
 */
export enum uricacidCheckResultEnum {
  Other = 0,
  Normal,
  Abnormal,
  Anomaly
}

/** 任务同步状态
 * @param { number } Success 配置成功
 * @param { number } AddLoading 新增中
 * @param { number } UpdateLoading 修改中
 * @param { number } DelLoading 删除中
 * @param { number } DelSuccess 删除成功
 */
export enum missionStatusEnum {
  Success = 1,
  AddLoading,
  UpdateLoading,
  DelLoading,
  DelSuccess
}

/** 奖励类型
 * @param { number } money 现金
 */
export enum missionAwardTypeEnum {
  money = 1
}

/** 性别枚举
 * @param { number } male 男
 * @param { number } female 女
 * @param { number } other 其他
 */
export enum genderEnum {
  other = 0,
  male,
  female
}

/** 问诊分账枚举
 * @param { number } public 全局
 * @param { number } doctorBatch 医生批量
 * @param { number } gaBatch 村医批量
 */
export enum missionSettingTypeEnum {
  public = 1,
  doctorBatch,
  gpBatch
}

/** 任务的启用状态
 * @param { number } on 启用
 * @param { number } off 关闭
 * @param { number } configuring 配置中
 * @param { number } onLoading 启用中
 * @param { number } offLoading 关闭中
 */
export enum awardOnStateEnum {
  on = 1,
  off,
  configuring,
  onLoading,
  offLoading
}

/** 用药依从性
 * @param { number } regular 规律
 * @param { number } intermittent 间断
 * @param { number } nomedication 不用药
 */
export enum complianceEnum {
  regular = 1,
  intermittent,
  nomedication
}

/** 不良反应 */
export enum adverseReactionEnum {
  no = 0,
  yes
}
