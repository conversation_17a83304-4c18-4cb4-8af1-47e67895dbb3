/** 请求状态的枚举
 * @param { number } error 失败
 * @param { number } success 成功
 */
 export enum requestStatus {
    error = -1,
    success = 0,
  }
  
  /** 性别枚举
   * @param {nunber} man 1 男
   * @param {nunber} woman 2 女
   */
  export enum sexEnum {
    man = 1,
    woman,
  }
  
  /** 是否快照枚举
   * @param { number } noSnapshot 不是快照
   * @param { number } hasSnapshot 是快照
   */
  export enum snapshotEnum {
    noSnapshot = 0,
    hasSnapshot,
  }
  
  /** 问诊订单状态
   * @param { numbrer } all 全部订单
   * @param { numbrer } onpay 未支付(待付款)
   * @param { numbrer } oncall 已支付(待接诊)
   * @param { numbrer } call 已支付(问诊中)
   * @param { numbrer } complete 已完成
   * @param { numbrer } nocall 未接诊
   * @param { numbrer } cancel 已取消
   */
  export enum orderStatusEnum {
    oncall = 0,
    call,
    complete,
    nocall,
    cancel,
  }
  
  /** 问诊房间类型（与云信无关）
   * @param { number } ImgText 图文
   * @param { number } audio 音频
   * @param { number } video 视频
   * @param { number } consultation 1对1会诊
   * @param { number } multiConsultation 多方会诊
   */
  export enum roomType {
    ImgText = 1,
    audio,
    video,
    consultation,
    multiConsultation,
  }
  /** 随访状态
   * @param { number } wait 待填写
   * @param { number } complete 已填写
   */
  export enum visitStatus {
    wait = 0,
    complete,
  }
  
  /** 用户类型
   * @param { number } patient 患者
   * @param { number } doctor 医生
   * @param { number } opmsDoctor 平台医生
   * @param { number } villageDoctor 村医
   */
  export enum userType {
    patient = 1,
    doctor,
    opmsDoctor,
    villageDoctor,
  }
  
  /** 问诊/会诊在即时通讯接入中的状态
   * @param { number } nounderway 预约中
   * @param { number } underway 进行中
   * @param { number } finished 已结束
   */
  export enum consultationStatus {
    nounderway = 0,
    underway,
    finished,
  }
  
  /** 处方枚举
   * @param { number } prescribed 处方已开待审核
   * @param { number } check 药师已经审核通过
   * @param { number } pay 患者已经支付购买处方药品
   * @param { number } faild 药师审核不通过，无法进行
   * @param { number } cancel 医生主动撤销处方单，需要重新开具处方单
   * @param { number } expiry 处方单已经超时，失效（无法再使用）
   */
  export enum prescriptionEnum {
    prescribed = 1,
    check,
    pay,
    faild,
    cancel,
    expiry,
  }
  
  /** 图文聊天中消息读取状态
   * @param { number } noread 未读
   * @param { number } read 已读
   */
  export enum readStatus {
    noread = 1,
    read,
  }
  
  /** 是否评价
   * @param { number } noRated 未评价
   * @param { number } rated 已评价
   */
  export enum isEvaluateEnum {
    noRated = 0,
    rated,
  }
  
  export enum reqStatusEnum {
    error = 0,
    success,
  }
  
  /** rtc 通话类型
   * @param { number } audio 音频
   * @param { number } video 视频
   */
  export enum rtcType {
    audio = 1,
    video,
  }
  