import request from '@/config/axios'

export interface UserVO {
  name: string
  headImg: string
  phone: string
  birthday: Date
  sex: number
  areaCode?: number 

}

export interface UserPageReqVO extends PageParam {
  name?: string
  phone?: string
  createTime?: Date[]
  sex?: number
}

export interface UserExcelReqVO {
  name?: string
  phone?: string
  createTime?: Date[]
  sex?: number
}

// 查询患者账号信息列表
export const getUserPageApi = async (params: UserPageReqVO) => {
  return await request.get({ url: '/patient/user/page', params })
}

// 查询患者账号信息详情
export const getUserApi = async (id: number) => {
  return await request.get({ url: '/patient/user/get?id=' + id })
}

// 新增患者账号信息
export const createUserApi = async (data: UserVO) => {
  return await request.post({ url: '/patient/user/create', data })
}

// 修改患者账号信息
export const updateUserApi = async (data: UserVO) => {
  return await request.put({ url: '/patient/user/update', data })
}

// 删除患者账号信息
export const deleteUserApi = async (id: number) => {
  return await request.delete({ url: '/patient/user/delete?id=' + id })
}

// 导出患者账号信息 Excel
export const exportUserApi = async (params: UserExcelReqVO) => {
  return await request.download({ url: '/patient/user/export-excel', params })
}



// 根据患者用户id集合 获得患者信息
export const getUserByIds = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/patient/user/list?ids=' + ids })
}