import request from '@/config/axios'

export interface AdvertisingVO {
  id: number
  project: number
  clientType: number
  positionType: number
  versionNum: number,
  duration:number
}

export interface AdvertisingPageReqVO extends PageParam {
  project?: number
  clientType?: number
  positionType?: number
  versionNum?: number
  createTime?: Date[]
}

export interface AdvertisingExcelReqVO {
  project?: number
  clientType?: number
  positionType?: number
  versionNum?: number
  createTime?: Date[]
}

// 查询广告配置列表
export const getAdvertisingPageApi = async (params: AdvertisingPageReqVO) => {
  return await request.get({ url: '/operate/advertising/page', params })
}

// 查询广告配置详情
export const getAdvertisingApi = async (id: number) => {
  return await request.get({ url: '/operate/advertising/get?id=' + id })
}

// 新增广告配置
export const createAdvertisingApi = async (data: AdvertisingVO) => {
  return await request.post({ url: '/operate/advertising/create', data })
}

// 修改广告配置
export const updateAdvertisingApi = async (data: AdvertisingVO) => {
  return await request.put({ url: '/operate/advertising/update', data })
}

// 删除广告配置
export const deleteAdvertisingApi = async (id: number) => {
  return await request.delete({ url: '/operate/advertising/delete?id=' + id })
}

// 导出广告配置 Excel
export const exportAdvertisingApi = async (params: AdvertisingExcelReqVO) => {
  return await request.download({ url: '/operate/advertising/export-excel', params })
}
