import request from '@/config/axios'

export interface DoctorCollectVO {
  id: number
  patientId: number
  doctorId: number
  collectType: number
}

export interface DoctorCollectPageReqVO extends PageParam {
  patientId?: number
  doctorId?: number
  collectType?: number
}

export interface DoctorCollectExcelReqVO {
  patientId?: number
  doctorId?: number
  collectType?: number
}

// 查询收藏医生列表
export const getDoctorCollectPageApi = async (params: DoctorCollectPageReqVO) => {
  return await request.get({ url: '/patient/doctor-collect/page', params })
}

// 查询收藏医生详情
export const getDoctorCollectApi = async (id: number) => {
  return await request.get({ url: '/patient/doctor-collect/get?id=' + id })
}

// 新增收藏医生
export const createDoctorCollectApi = async (data: DoctorCollectVO) => {
  return await request.post({ url: '/patient/doctor-collect/create', data })
}

// 修改收藏医生
export const updateDoctorCollectApi = async (data: <PERSON><PERSON>oll<PERSON>t<PERSON>) => {
  return await request.put({ url: '/patient/doctor-collect/update', data })
}

// 删除收藏医生
export const deleteDoctorCollectApi = async (id: number) => {
  return await request.delete({ url: '/patient/doctor-collect/delete?id=' + id })
}

// 导出收藏医生 Excel
export const exportDoctorCollectApi = async (params: DoctorCollectExcelReqVO) => {
  return await request.download({ url: '/patient/doctor-collect/export-excel', params })
}
