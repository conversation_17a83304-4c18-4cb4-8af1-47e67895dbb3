import request from '@/config/axios'
import { DrugCountStatsEChartData,DrugMoneyStatsEChartData,DrugMoneyStatsEChartDataItem } from '@/api/rx/stats/types'


// 查询患者注册统计信息
export const getDrugCountStatsDataApi = async (params: any):Promise<DrugCountStatsEChartData> => {
  const drugCountStatsApiData=await request.get({ url: '/rx/drug-sales-stats/find-drug-sales-count?type=' + params+'&top=10' })
  const drugCountStatsData: DrugCountStatsEChartData={
    dataName: [],
    dataValue: []
  }
  for(const item of drugCountStatsApiData){
    drugCountStatsData.dataName.push(item.drugName)
    drugCountStatsData.dataValue.push(item.salesCount)
    }
  return drugCountStatsData
}

// 查询患者注册统计信息
export const getDrugMoneyStatsDataApi = async (params: any):Promise<DrugMoneyStatsEChartData> => {
  const drugMoneyStatsApiData=await request.get({ url: '/rx/drug-sales-stats/find-drug-sales-money?type=' + params+'&top=10' })
  const drugMoneyStatsData: DrugMoneyStatsEChartData={
    data: [],
    dataName: [],
    dataAllCount: ''
  }
  let allMoney=0
  for(const item of drugMoneyStatsApiData){
    const dataItem:DrugMoneyStatsEChartDataItem={
      name:item.drugName,
      value:item.salesMoney.toFixed(2)
    }
    drugMoneyStatsData.data.push(dataItem)
    drugMoneyStatsData.dataName.push(item.drugName)
    allMoney=allMoney+item.salesMoney
    }
    drugMoneyStatsData.dataAllCount=allMoney.toFixed(2)
  return drugMoneyStatsData
}