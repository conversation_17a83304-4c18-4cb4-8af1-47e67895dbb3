import request from '@/config/axios'
import { formatAreaToCascader } from '@/utils/area'

export interface AreaVO {
  id: number
  code: number
  name: string
  parentCode: number
  level: number
  nameAbbr: string
  regular: string
  sort: number
  about: string
}

export interface AreaPageReqVO extends PageParam {
  code?: number
  name?: string
  parentCode?: number
  createTime?: Date[]
}

export interface AreaExcelReqVO {
  code?: number
  name?: string
  parentCode?: number
  createTime?: Date[]
}

export interface AreaByCascaderVo {
  value: number
  label: string
  children?: AreaByCascaderVo[]
}

// 查询地区信息列表
export const getAreaPageApi = async (params: AreaPageReqVO) => {
  return await request.get({ url: '/operate/area/page', params })
}

// 查询地区信息详情
export const getAreaApi = async (id: number) => {
  return await request.get({ url: '/operate/area/get?id=' + id })
}

// 新增地区信息
export const createAreaApi = async (data: AreaVO) => {
  return await request.post({ url: '/operate/area/create', data })
}

// 修改地区信息
export const updateAreaApi = async (data: AreaVO) => {
  return await request.put({ url: '/operate/area/update', data })
}

// 删除地区信息
export const deleteAreaApi = async (id: number) => {
  return await request.delete({ url: '/operate/area/delete?id=' + id })
}

// 导出地区信息 Excel
export const exportAreaApi = async (params: AreaExcelReqVO) => {
  return await request.download({ url: '/operate/area/export-excel', params })
}

// 查询所有地区信息
export const getAreaAllApi = (() => {
  let cache: AreaVO[] = []
  return async () => {
    if (cache && cache.length > 0) return cache
    let res: AreaVO[] =
      (await request.get({ url: '/assets/json/area.json', base: import.meta.env.BASE_URL })) || []

    if (res.length <= 0) res = await request.get({ url: '/operate/area/find-area-list' })

    cache = res
    return res
  }
})()

// 获取Cascader版的城市数据
export const getAreaByCascader = async (): Promise<AreaByCascaderVo[]> => {
  let res: AreaByCascaderVo[] =
    (await request.get({
      url: '/assets/json/areacaseader.json',
      base: import.meta.env.BASE_URL
    })) || []

  if (res.length <= 0) res = await formatAreaToCascader()

  return res
}
