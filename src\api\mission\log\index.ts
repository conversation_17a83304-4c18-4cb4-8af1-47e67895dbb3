import request from '@/config/axios'

export interface LogVO {
  id: number
  missionId: number
  successFate: number
  failedFate: number
  total: number
  missionTimestamp: number
}

export interface LogPageReqVO extends PageParam {
  missionId?: number
  successFate?: number
  failedFate?: number
  total?: number
  missionTimestamp?: number
  createTime?: Date[]
}

export interface LogExcelReqVO {
  missionId?: number
  successFate?: number
  failedFate?: number
  total?: number
  missionTimestamp?: number
  createTime?: Date[]
}

// 查询任务执行日志列表
export const getLogPageApi = async (params: LogPageReqVO) => {
  return await request.get({ url: '/mission/log/page', params })
}

// 查询任务执行日志详情
export const getLogApi = async (id: number) => {
  return await request.get({ url: '/mission/log/get?id=' + id })
}

// 新增任务执行日志
export const createLogApi = async (data: LogVO) => {
  return await request.post({ url: '/mission/log/create', data })
}

// 修改任务执行日志
export const updateLogApi = async (data: LogVO) => {
  return await request.put({ url: '/mission/log/update', data })
}

// 删除任务执行日志
export const deleteLogApi = async (id: number) => {
  return await request.delete({ url: '/mission/log/delete?id=' + id })
}

// 导出任务执行日志 Excel
export const exportLogApi = async (params: LogExcelReqVO) => {
  return await request.download({ url: '/mission/log/export-excel', params })
}
