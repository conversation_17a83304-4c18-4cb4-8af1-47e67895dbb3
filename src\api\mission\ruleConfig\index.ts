import request from '@/config/axios'

export interface RuleConfigVO {
  id: number
  ruleName: string
  ruleCode: string
  ruleCompleteConfig: string
  ruleAwardType: number
  ruleRepetition: boolean
}

export interface RuleConfigPageReqVO extends PageParam {
  ruleName?: string
  ruleCode: string
  ruleCompleteConfig?: string
  ruleAwardType?: number
  ruleRepetition?: boolean
  createTime?: number[]
}

export interface RuleConfigExcelReqVO {
  ruleName?: string
  ruleCode: string
  ruleCompleteConfig?: string
  ruleAwardType?: number
  ruleRepetition?: boolean
  createTime?: number[]
}

// 查询任务规则配置列表
export const getRuleConfigPageApi = async (params: RuleConfigPageReqVO) => {
  return await request.get({ url: '/mission/rule-config/page', params })
}

export const getRuleListApi = async (ids:Array<number>)=>{
  if(ids.length<=0) return []
  return await request.get({url:'/mission/rule-config/list?ids='+ids})
}

export const getAllRuleConfigListApi = async ()=>{
  return await request.get({url:'/mission/rule-config/list-all'})
}




// 查询任务规则配置详情
export const getRuleConfigApi = async (id: number) => {
  return await request.get({ url: '/mission/rule-config/get?id=' + id })
}

// 新增任务规则配置
export const createRuleConfigApi = async (data: RuleConfigVO) => {
  return await request.post({ url: '/mission/rule-config/create', data })
}

// 修改任务规则配置
export const updateRuleConfigApi = async (data: RuleConfigVO) => {
  return await request.put({ url: '/mission/rule-config/update', data })
}

// 删除任务规则配置
export const deleteRuleConfigApi = async (id: number) => {
  return await request.delete({ url: '/mission/rule-config/delete?id=' + id })
}

// 导出任务规则配置 Excel
export const exportRuleConfigApi = async (params: RuleConfigExcelReqVO) => {
  return await request.download({ url: '/mission/rule-config/export-excel', params })
}
