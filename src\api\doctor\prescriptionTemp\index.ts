import request from '@/config/axios'

export interface PrescriptionTempVO {
  id: number
  doctorId: number
  illnessType: number
  departCode: string
  drugType: number
  medicalCertificate: string
  diagnoseSuggest: string
}

export interface PrescriptionTempPageReqVO extends PageParam {
  doctorId?: number
}

export interface PrescriptionTempExcelReqVO {
  doctorId?: number
}

// 查询医生处方模板列表
export const getPrescriptionTempPageApi = async (params: PrescriptionTempPageReqVO) => {
  return await request.get({ url: '/doctor/prescription-temp/page', params })
}

// 查询医生处方模板详情
export const getPrescriptionTempApi = async (id: number) => {
  return await request.get({ url: '/doctor/prescription-temp/get?id=' + id })
}

// 新增医生处方模板
export const createPrescriptionTempApi = async (data: PrescriptionTempVO) => {
  return await request.post({ url: '/doctor/prescription-temp/create', data })
}

// 修改医生处方模板
export const updatePrescriptionTempApi = async (data: PrescriptionTempVO) => {
  return await request.put({ url: '/doctor/prescription-temp/update', data })
}

// 删除医生处方模板
export const deletePrescriptionTempApi = async (id: number) => {
  return await request.delete({ url: '/doctor/prescription-temp/delete?id=' + id })
}

// 导出医生处方模板 Excel
export const exportPrescriptionTempApi = async (params: PrescriptionTempExcelReqVO) => {
  return await request.download({ url: '/doctor/prescription-temp/export-excel', params })
}

// 查询医生处方模板详情
export const getPrescriptionTempByDoctorId = async (params: PrescriptionTempPageReqVO) => {
  return await request.get({
    url: '/doctor/prescription-temp/page-prescription-by-doctorid',
    params
  })
}
