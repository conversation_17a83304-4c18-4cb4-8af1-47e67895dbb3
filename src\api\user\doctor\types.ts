export type UserVO = {
  id: number
  account: string
  password: string
  name: string
  birthday: string
  phone: string
  headImg: string
  hospitalDepartId: number
  officeHolderCode: string
  beGoodAt: string
  about: string
  sex: number
  doctorType: number
  hospitalId: number
  hospitalDepart: string
  idCard: string
  departId: number
  districtCode: number
  officeHolderId: number
  rankId: number
  rankName: string
  status: number
  isAuth: number
  visitCount: number
  evaAvg: number
  evaCount: number
  score: number
  receptionDuration: number
}

export type UserPageReqVO = {
  account: string
  name: string
  phone: string
  sex: number
  doctorType: number
  hospitalId: number
  hospitalDepart: string
  idCard: string
  departId: number
  districtCode: number
  officeHolderId: number
  rankId: number
  rankName: string
  status: number
  isAuth: number
  visitCount: number
  evaAvg: number
  evaCount: number
  score: number
  receptionDuration: number
}

export type UserExcelReqVO = {
  account: string
  name: string
  phone: string
  sex: number
  doctorType: number
  hospitalId: number
  hospitalDepart: string
  idCard: string
  departId: number
  districtCode: number
  officeHolderId: number
  rankId: number
  rankName: string
  status: number
  isAuth: number
  visitCount: number
  evaAvg: number
  evaCount: number
  score: number
  receptionDuration: number
}