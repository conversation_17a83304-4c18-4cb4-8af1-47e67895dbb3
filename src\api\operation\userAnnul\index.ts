import request from '@/config/axios'

export interface UserAnnulVO {
  status: number
}

export interface UserAnnulPageReqVO extends PageParam {
  userId?: number
  userType?: number
  status?: number
  applyContent?: string
}

export interface UserAnnulExcelReqVO {
  userId?: number
  userType?: number
  status?: number
  applyContent?: string
}

// 查询用户注销记录列表
export const getUserAnnulPageApi = async (params: UserAnnulPageReqVO) => {
  return await request.get({ url: '/operate/user-annul/page', params })
}

// 查询用户注销记录详情
export const getUserAnnulApi = async (id: number) => {
  return await request.get({ url: '/operate/user-annul/get?id=' + id })
}

// 新增用户注销记录
export const createUserAnnulApi = async (data: UserAnnulVO) => {
  return await request.post({ url: '/operate/user-annul/create', data })
}

// 修改用户注销记录
export const updateUserAnnulApi = async (data: UserAnnulVO) => {
  return await request.put({ url: '/operate/user-annul/update', data })
}

// 删除用户注销记录
export const deleteUserAnnulApi = async (id: number) => {
  return await request.delete({ url: '/operate/user-annul/delete?id=' + id })
}

// 导出用户注销记录 Excel
export const exportUserAnnulApi = async (params: UserAnnulExcelReqVO) => {
  return await request.download({ url: '/operate/user-annul/export-excel', params })
}
