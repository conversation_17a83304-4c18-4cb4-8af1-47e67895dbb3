import request from '@/config/axios'

export interface DispatchUserVO {
  id?: number
  gpId: number
  doctorId: number
  sort?:number
}

export interface DispatchUserPageReqVO extends PageParam {
  gpId?: number
  doctorId?: number
  createTime?: Date[]
}

export interface DispatchUserExcelReqVO {
  gpId?: number
  doctorId?: number
  createTime?: Date[]
}

// 查询用户调度医生设置列表
export const getDispatchUserPageApi = async (params: DispatchUserPageReqVO) => {
  return await request.get({ url: '/doctor/dispatch-user/page', params })
}

// 查询用户调度医生设置详情
export const getDispatchUserApi = async (id: number) => {
  return await request.get({ url: '/doctor/dispatch-user/get?id=' + id })
}

// 新增用户调度医生设置
export const createDispatchUserApi = async (data: DispatchUserVO) => {
  return await request.post({ url: '/doctor/dispatch-user/create', data })
}

// 修改用户调度医生设置
export const updateDispatchUserApi = async (data: DispatchUserVO) => {
  return await request.put({ url: '/doctor/dispatch-user/update', data })
}

// 删除用户调度医生设置
export const deleteDispatchUserApi = async (id: number) => {
  return await request.delete({ url: '/doctor/dispatch-user/delete?id=' + id })
}

// 导出用户调度医生设置 Excel
export const exportDispatchUserApi = async (params: DispatchUserExcelReqVO) => {
  return await request.download({ url: '/doctor/dispatch-user/export-excel', params })
}

export const getDispatchUserListByGpIdApi = async (gpId: number) => {
  return await request.get({ url: `/doctor/dispatch-user/get-doctor-by-gp-id?gpId=${gpId}` })
}
