export type TaskAwardGroupVO = {
  id: number
  type: number
  awardIds: string
  taskCount: number
  peopleCount: number
  unitPrice: bigdecimal
  awardPrice: bigdecimal
  repeatCount: number
  isRepeat: number
  awardType: number
  status: number
  userCheckRepeat: number
  healthCount: number
  healthType: string
  awardStartTime: string
  awardEndTime: string
  jobTime: string
  enable: number
}

export type TaskAwardGroupPageReqVO = {
  type: number
  awardIds: string
  taskCount: number
  peopleCount: number
  unitPrice: bigdecimal
  awardPrice: bigdecimal
  repeatCount: number
  isRepeat: number
  awardType: number
  status: number
  userCheckRepeat: number
  healthCount: number
  healthType: string
  awardStartTime: string
  awardEndTime: string
  jobTime: string
  enable: number
  createTime: string
}

export type TaskAwardGroupExcelReqVO = {
  type: number
  awardIds: string
  taskCount: number
  peopleCount: number
  unitPrice: bigdecimal
  awardPrice: bigdecimal
  repeatCount: number
  isRepeat: number
  awardType: number
  status: number
  userCheckRepeat: number
  healthCount: number
  healthType: string
  awardStartTime: string
  awardEndTime: string
  jobTime: string
  enable: number
  createTime: string
}