import request from '@/config/axios'

export interface ClientUpdateVO {
  id: number
  version: string
  versionNum: number
  updateType: number
  lastForceVersionNum: number
  updateContent: string
  md5: string
  updateUrl: string
  effectTime: Date
  resourceNum: number
  project: number
  clientType: number
}

export interface ClientUpdatePageReqVO extends PageParam {
  version?: string
  versionNum?: number
  updateType?: number
  lastForceVersionNum?: number
  updateContent?: string
  md5?: string
  updateUrl?: string
  effectTime?: Date[]
  createTime?: Date[]
  resourceNum?: number
  project?: number
  clientType?: number
}

export interface ClientUpdateExcelReqVO {
  version?: string
  versionNum?: number
  updateType?: number
  lastForceVersionNum?: number
  updateContent?: string
  md5?: string
  updateUrl?: string
  effectTime?: Date[]
  createTime?: Date[]
  resourceNum?: number
  project?: number
  clientType?: number
}

// 查询客户端更新列表
export const getClientUpdatePageApi = async (params: ClientUpdatePageReqVO) => {
  return await request.get({ url: '/operate/client-update/page', params })
}

// 查询客户端更新详情
export const getClientUpdateApi = async (id: number) => {
  return await request.get({ url: '/operate/client-update/get?id=' + id })
}

// 新增客户端更新
export const createClientUpdateApi = async (data: ClientUpdateVO) => {
  return await request.post({ url: '/operate/client-update/create', data })
}

// 修改客户端更新
export const updateClientUpdateApi = async (data: ClientUpdateVO) => {
  return await request.put({ url: '/operate/client-update/update', data })
}

// 删除客户端更新
export const deleteClientUpdateApi = async (id: number) => {
  return await request.delete({ url: '/operate/client-update/delete?id=' + id })
}

// 导出客户端更新 Excel
export const exportClientUpdateApi = async (params: ClientUpdateExcelReqVO) => {
  return await request.download({ url: '/operate/client-update/export-excel', params })
}
