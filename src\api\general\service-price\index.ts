import request from '@/config/axios'

export interface ServicePriceVO {
  id: number
  firstDepart: number
  secondDepart: number
  serviceType: number
  chiefPhysician: number
  associateChiefPhysician: number
  visitingStaff: number
  residentDoctor: number
  feldsher: number
}

export interface ServicePricePageReqVO extends PageParam {
  firstDepart?: number
  secondDepart?: number
  serviceType?: number
  chiefPhysician?: number
  associateChiefPhysician?: number
  visitingStaff?: number
  residentDoctor?: number
  feldsher?: number
  createTime?: Date[]
}

export interface ServicePriceExcelReqVO {
  firstDepart?: number
  secondDepart?: number
  serviceType?: number
  chiefPhysician?: number
  associateChiefPhysician?: number
  visitingStaff?: number
  residentDoctor?: number
  feldsher?: number
  createTime?: Date[]
}

// 查询服务价格列表
export const getServicePricePageApi = async (params: ServicePricePageReqVO) => {
  return await request.get({ url: '/operate/service-price/page', params })
}

// 查询服务价格详情
export const getServicePriceApi = async (id: number) => {
  return await request.get({ url: '/operate/service-price/get?id=' + id })
}

// 新增服务价格
export const createServicePriceApi = async (data: ServicePriceVO) => {
  return await request.post({ url: '/operate/service-price/create', data })
}

// 修改服务价格
export const updateServicePriceApi = async (data: ServicePriceVO) => {
  return await request.put({ url: '/operate/service-price/update', data })
}

// 删除服务价格
export const deleteServicePriceApi = async (id: number) => {
  return await request.delete({ url: '/operate/service-price/delete?id=' + id })
}

// 导出服务价格 Excel
export const exportServicePriceApi = async (params: ServicePriceExcelReqVO) => {
  return await request.download({ url: '/operate/service-price/export-excel', params })
}
