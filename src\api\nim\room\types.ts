export type RoomVO = {
  id: number
  orderId: number
  orderNo: string
  orderType: number
  accountId: number
  templateId: number
  roomArchiveId: string
  roomUuid: string
  roomName: string
  roomPassword: string
  roomWhiteboard: boolean
  roomChatroom: boolean
  roomLive: boolean
  roomRtc: boolean
  roomSip: boolean
  roomRecord: boolean
  roomSeat: boolean
}

export type RoomPageReqVO = {
  orderId: number
  orderNo: string
  orderType: number
  accountId: number
  templateId: number
  roomArchiveId: string
  roomUuid: string
  roomName: string
  createTime: string
}

export type RoomExcelReqVO = {
  orderId: number
  orderNo: string
  orderType: number
  accountId: number
  templateId: number
  roomArchiveId: string
  roomUuid: string
  roomName: string
  createTime: string
}