export type VisitIndependentGroupVO = {
  id: number
  visitType: number
  independentIds: string
  awardType: number
  status: number
  settlementIntervalStart: number
  settlementIntervalEnd: number
  settingType: number
  startTime: string
  endTime: string
  jobTime: string
}

export type VisitIndependentGroupPageReqVO = {
  visitType: number
  independentIds: string
  awardType: number
  status: number
  settlementIntervalStart: number
  settlementIntervalEnd: number
  settingType: number
  startTime: string
  endTime: string
  jobTime: string
  createTime: string
}

export type VisitIndependentGroupExcelReqVO = {
  visitType: number
  independentIds: string
  awardType: number
  status: number
  settlementIntervalStart: number
  settlementIntervalEnd: number
  settingType: number
  startTime: string
  endTime: string
  jobTime: string
  createTime: string
}