import request from '@/config/axios'

export interface AppointmentVO {
  id: number
  patientId: number
  sickId: number
  doctorId: number
  appointmentCode: string
  useTime: Date
  expireTime: Date
  status: number
}

export interface AppointmentPageReqVO extends PageParam {
  patientId?: number
}

export interface AppointmentExcelReqVO {
  patientId?: number
}

// 查询预约单列表
export const getAppointmentPageApi = async (params: AppointmentPageReqVO) => {
  return await request.get({ url: '/patient/appointment/page', params })
}

// 查询预约单详情
export const getAppointmentApi = async (id: number) => {
  return await request.get({ url: '/patient/appointment/get?id=' + id })
}

// 新增预约单
export const createAppointmentApi = async (data: AppointmentVO) => {
  return await request.post({ url: '/patient/appointment/create', data })
}

// 修改预约单
export const updateAppointmentApi = async (data: AppointmentVO) => {
  return await request.put({ url: '/patient/appointment/update', data })
}

// 删除预约单
export const deleteAppointmentApi = async (id: number) => {
  return await request.delete({ url: '/patient/appointment/delete?id=' + id })
}

// 导出预约单 Excel
export const exportAppointmentApi = async (params: AppointmentExcelReqVO) => {
  return await request.download({ url: '/patient/appointment/export-excel', params })
}


// 获得预约单列表
export const getAppointmentVisitApi = async (visitIds: number[],visitType:number) => {
  return await request.get({ url: `/patient/appointment/list-visit?visitIds=${visitIds}&visitType=${visitType}` })
}

// 获得医生评价
export const getEvaluatesByVisitTypeApi = async (serviceId: number,serviceType:number) => {
  return await request.get({ url: `/doctor/evaluate/find-evaluates-by-visit-type?serviceId=${serviceId}&serviceType=${serviceType}` })
}