import request from '@/config/axios'
import { UserRegisterStatsEChartData, VisitOrderStatsEChartData,DrugOrderStatsEChartData } from '@/api/user/patient/stats/types'
import { ge_time_format } from '@/utils/custom'


// 查询患者注册统计信息
export const getUserRegisterDataApi = async (params: any): Promise<UserRegisterStatsEChartData> => {
  const userRegisterStatsApiData = await request.get({ url: '/patient/user-register-stats/find-user-register-stats?type=' + params + '&pageNo=1&pageSize=10' })
  const userRegisterStatsData: UserRegisterStatsEChartData = {
    dataName: [],
    dataValue: []
  }
  for (const item of userRegisterStatsApiData.reverse()) {
    const statsStampFormat = params == 1 ? '2' : (params == 2 ? '3' : '11')
    userRegisterStatsData.dataName.push(ge_time_format(item.statsTimeStamp, statsStampFormat))
    userRegisterStatsData.dataValue.push(item.count)
  }
  return userRegisterStatsData
}

export const getVisitOrderDataApi = async (params: any): Promise<VisitOrderStatsEChartData> => {
  const visitOrderStatsApiData = await request.get({ url: '/patient/visit-stats/find-visit-order-stats?type=' + params + '&pageNo=1&pageSize=10' })
  const visitOrderStatsData: VisitOrderStatsEChartData = {
    dataName: [],
    dataAllValue: [],
    dataTextValue: [],
    dataAudioValue: [],
    dataVideoValue: []
  }
  for (const item of visitOrderStatsApiData.reverse()) {
    const statsStampFormat = params == 1 ? '2' : (params == 2 ? '3' : '11')
    visitOrderStatsData.dataName.push(ge_time_format(item.statsTimeStamp, statsStampFormat))
    visitOrderStatsData.dataAllValue.push(item.allCount)
    visitOrderStatsData.dataTextValue.push(item.textCount)
    visitOrderStatsData.dataAudioValue.push(item.audioCount)
    visitOrderStatsData.dataVideoValue.push(item.videoCount)
  }
  return visitOrderStatsData
}

export const getDrugOrderDataApi = async (params: any): Promise<DrugOrderStatsEChartData> => {
  const drugOrderStatsApiData = await request.get({ url: '/patient/drug-order-stats/find-drug-order-stats?type=' + params + '&pageNo=1&pageSize=10' })
  const drugOrderStatsData: DrugOrderStatsEChartData = {
    dataName: [],
    dataAllValue: [],
    dataPayValue:[]
  }
  for (const item of drugOrderStatsApiData.reverse()) {
    const statsStampFormat = params == 1 ? '2' : (params == 2 ? '3' : '11')
    drugOrderStatsData.dataName.push(ge_time_format(item.statsTimeStamp, statsStampFormat))
    drugOrderStatsData.dataAllValue.push(item.allCount)
    drugOrderStatsData.dataPayValue.push(item.payCount)
  }
  return drugOrderStatsData
}