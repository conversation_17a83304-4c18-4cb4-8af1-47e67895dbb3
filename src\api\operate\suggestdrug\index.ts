import request from '@/config/axios'

export interface SuggestDrugVO {
  id: number
  suggestId: number
  drugId: number
  count: number
  frequencyId: number
  frequencyText: string
  quantityText: string
  methodId: number
  methodText: string
  isPrescriptionDrug: number
}

export interface SuggestDrugPageReqVO extends PageParam {
  suggestId?: number
  drugId?: number
  count?: number
  frequencyId?: number
  frequencyText?: string
  quantityText?: string
  methodId?: number
  methodText?: string
  isPrescriptionDrug?: number
  createTime?: Date[]
}

export interface SuggestDrugExcelReqVO {
  suggestId?: number
  drugId?: number
  count?: number
  frequencyId?: number
  frequencyText?: string
  quantityText?: string
  methodId?: number
  methodText?: string
  isPrescriptionDrug?: number
  createTime?: Date[]
}

// 查询患者诊断建议药品信息列表
export const getSuggestDrugPageApi = async (params: SuggestDrugPageReqVO) => {
  return await request.get({ url: '/patient/suggest-drug/page', params })
}

// 查询患者诊断建议药品信息详情
export const getSuggestDrugApi = async (id: number) => {
  return await request.get({ url: '/patient/suggest-drug/get?id=' + id })
}

// 新增患者诊断建议药品信息
export const createSuggestDrugApi = async (data: SuggestDrugVO) => {
  return await request.post({ url: '/patient/suggest-drug/create', data })
}

// 修改患者诊断建议药品信息
export const updateSuggestDrugApi = async (data: SuggestDrugVO) => {
  return await request.put({ url: '/patient/suggest-drug/update', data })
}

// 删除患者诊断建议药品信息
export const deleteSuggestDrugApi = async (id: number) => {
  return await request.delete({ url: '/patient/suggest-drug/delete?id=' + id })
}

// 导出患者诊断建议药品信息 Excel
export const exportSuggestDrugApi = async (params: SuggestDrugExcelReqVO) => {
  return await request.download({ url: '/patient/suggest-drug/export-excel', params })
}


export const getSuggestDrugListSuggestApi = async (id: number) => {
  return await request.get({ url: '/patient/suggest-drug/list-suggest?id=' + id })
}
