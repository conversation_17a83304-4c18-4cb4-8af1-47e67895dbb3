import request from '@/config/axios'

export interface AwardSystemAccountVO {
  id: number
  type: number
  groupId: number
  userType: number
  doctorRate: number
  gpRate: number
  missionId: number
  status: number
  stopStatus: number
}

export interface AwardSystemAccountPageReqVO extends PageParam {
  type?: number
  groupId?: number
  userType?: number
  doctorRate?: number
  gpRate?: number
  missionId?: number
  status?: number
  stopStatus?: number
  createTime?: Date[]
}

export interface AwardSystemAccountExcelReqVO {
  type?: number
  groupId?: number
  userType?: number
  doctorRate?: number
  gpRate?: number
  missionId?: number
  status?: number
  stopStatus?: number
  createTime?: Date[]
}

// 查询任务系统分配子项列表
export const getAwardSystemAccountPageApi = async (params: AwardSystemAccountPageReqVO) => {
  return await request.get({ url: '/operate/award-system-account/page', params })
}

// 查询任务系统分配子项详情
export const getAwardSystemAccountApi = async (id: number) => {
  return await request.get({ url: '/operate/award-system-account/get?id=' + id })
}

// 新增任务系统分配子项
export const createAwardSystemAccountApi = async (data: AwardSystemAccountVO) => {
  return await request.post({ url: '/operate/award-system-account/create', data })
}

// 修改任务系统分配子项
export const updateAwardSystemAccountApi = async (data: AwardSystemAccountVO) => {
  return await request.put({ url: '/operate/award-system-account/update', data })
}

// 删除任务系统分配子项
export const deleteAwardSystemAccountApi = async (id: number) => {
  return await request.delete({ url: '/operate/award-system-account/delete?id=' + id })
}

// 导出任务系统分配子项 Excel
export const exportAwardSystemAccountApi = async (params: AwardSystemAccountExcelReqVO) => {
  return await request.download({ url: '/operate/award-system-account/export-excel', params })
}

export const getAwardSystemAccountListByGroupIds = async (groupIds: number[], type: number) => {
  return await request.get({
    url: `/operate/award-system-account/list-by-group-ids?ids=${groupIds}&type=${type}`
  })
}