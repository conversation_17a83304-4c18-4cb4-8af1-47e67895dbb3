import request from '@/config/axios'

export interface UserVO {
  id: number
  account: string
  name: string
  password: string
  phone: string
  headImg: string
  rxId: number
  type: number
  isRealName: number
  status: number
  sex: number
}

export interface UserPageReqVO extends PageParam {
  account?: string
  name?: string
  password?: string
  phone?: string
  headImg?: string
  rxId?: number
  type?: number
  isRealName?: number
  status?: number
  createTime?: Date[]
  sex?: number
}

export interface UserExcelReqVO {
  account?: string
  name?: string
  password?: string
  phone?: string
  headImg?: string
  rxId?: number
  type?: number
  isRealName?: number
  status?: number
  createTime?: Date[]
  sex?: number
}

// 查询药店管理员列表
export const getUserPageApi = async (params: UserPageReqVO) => {
  return await request.get({ url: 'rx/user/page', params })
}

// 查询药店管理员详情
export const getUserApi = async (id: number) => {
  return await request.get({ url: 'rx/user/get?id=' + id })
}

// 新增药店管理员
export const createUserApi = async (data: UserVO) => {
  return await request.post({ url: 'rx/user/create', data })
}

// 修改药店管理员
export const updateUserApi = async (data: UserVO) => {
  return await request.put({ url: 'rx/user/update', data })
}

// 删除药店管理员
export const deleteUserApi = async (id: number) => {
  return await request.delete({ url: 'rx/user/delete?id=' + id })
}

// 导出药店管理员 Excel
export const exportUserApi = async (params: UserExcelReqVO) => {
  return await request.download({ url: 'rx/user/export-excel', params })
}


// 根据用户id集合 获得信息
export const getUserList = async (ids: Array<number>) => {
  if (ids.length <= 0) return []
  return await request.get({ url: '/rx/user/list?ids=' + ids })
}
