import { reactive } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
// import { DICT_TYPE } from '@/utils/dict'
// import { required } from '@/utils/formRules'
import { VxeCrudSchema, useVxeCrudSchemas } from '@/hooks/web/useVxeCrudSchemas'
const { t } = useI18n() // 国际化
// 表单校验
export const rules = reactive({
})
// CrudSchema
const crudSchemas = reactive<VxeCrudSchema>({
  primaryKey: 'id', // 默认的主键ID
  primaryTitle: t('common.index'), // 默认显示的值
  primaryType: 'seq', // 默认为seq，序号模式
  action: true,
  actionWidth: '200', // 3个按钮默认200，如有删减对应增减即可
  columns: [
    {
      title: '门店编号',
      field: 'num',
      isSearch: true,
    },
    {
      title: '门店名称',
      field: 'name',
      isSearch: true,
    },
    {
      title: '省级编码',
      field: 'provinceCode',
      isSearch: false,
      isTable:false,
      isDetail:false,
    },
    {
      title: '市级编码',
      field: 'cityCode',
      isSearch: false,
      isTable:false,
      isDetail:false,
    },  
    {
      title: '区级编码',
      field: 'districtCode',
      isSearch: false,
      isTable:false,
      isDetail:false, 
    },
    {
      title: '地区',
      field: 'areaCode',
      table: {
        // 显示id hospitalid_template 是 vue的template模板
        slots: {
          default: 'areaid_template'
        }
      },
      detail: {
        slots: 'areaid_template'
      },
      form: {
        component: 'Cascader',
        componentProps: {
          showAllLevels: false,
          options: [],
          props: {
            checkStrictly: true
          }
        }
      }
    },
    {
      title: '详细地址',
      field: 'address',
      isSearch: true,
    },
    {
      title: '税号',
      field: 'taxNum',
      isSearch: false,
      isTable:false,
    }, 
    {
      title: '排序',
      field: 'sort',
      isForm:false,
      isTable:false,
      isDetail:false,
      form: {
        component: 'InputNumber',
        value: 0
      },
      isSearch: false, 
    },
    {
      title: '创建时间',
      field: 'createTime',
      isForm: false,
      formatter: 'formatDate',
      search: {
        show: false,
        itemRender: {
          name: 'XDataTimePicker'
        }
      },
    }
  ]
})
export const { allSchemas } = useVxeCrudSchemas(crudSchemas)