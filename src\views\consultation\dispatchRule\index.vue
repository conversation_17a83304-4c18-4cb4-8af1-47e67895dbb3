<template>
  <div class="withdrawRule">
    <el-form :model="form" label-width="auto" style="max-width: 1200px" :disabled="!edit">
      <el-form-item label="提现限额:">
        <div style="width: 100%;">
          <vxe-table border :data="form.dispatchRules">
            <vxe-column field="label" title="评分指标" width="80">
              <template #default="{row}">
                <p>{{ scoreType.find(v=> v.type === row.scoreType)?.label }}</p>
              </template>
            </vxe-column>
            <vxe-column field="remark" title="说明" align="left">
              <template #default="{row}">
                <p v-for="item in row.remark" :key="item">{{ item }}</p>
              </template>
            </vxe-column>
            <vxe-column field="initScore" title="初始分" width="70" />
            <vxe-column field="specificRules" title="评分细则" align="left" width="280">
              <template #default="{row, rowIndex}">
                <div v-for="(item, index) in row.specificRules" :key="item.doctorScoreDetail" style="display: flex;">
                  <p style="flex: 1;">{{ specificRules.find(v=> v.doctorScoreDetail === item.doctorScoreDetail)?.label }}</p>
                  <el-input-number v-if="row.scoreType !== 'CONSULTATION'" v-model="form.dispatchRules[rowIndex].specificRules[index].value" :precision="1" :controls="false"/>
                </div>
              </template>
            </vxe-column>
            <vxe-column title="权重" width="180">
              <template #default="{rowIndex}">
                  <el-input-number v-model="form.dispatchRules[rowIndex].weight" :precision="2" :controls="false"/>
              </template>
            </vxe-column>
            <vxe-column title="状态" width="70">
              <template #default="{rowIndex}">
                  <el-switch
                    v-model="form.dispatchRules[rowIndex].status"
                    :active-value="1"
                    :inactive-value="0"
                  />
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </el-form-item>
      <el-form-item label="会诊时间段:">
        <el-time-picker v-model="form.consultationStartTime" placeholder="00:00:00" value-format="HH:mm:ss" />
        <span style="margin: 0 10px">~</span>
        <el-time-picker v-model="form.consultationEndTime" placeholder="23:59:59" value-format="HH:mm:ss" />
      </el-form-item>

      <el-form-item label="批次调度量:">
        <el-input-number v-model="form.batchDispatchCount" :controls="false" />
      </el-form-item>

      <el-form-item label="呼叫超时(s):">
        <el-input-number v-model="form.callTimeOut" :controls="false" />
      </el-form-item>

      <el-form-item label="会诊间隔(s):">
        <el-input-number v-model="form.consultationInterval" :controls="false" />
      </el-form-item>

      <el-form-item label="特邀抢单触发时间(s):">
        <el-input-number v-model="form.grabOrderTriggerTime" :controls="false" />
      </el-form-item>

      <el-form-item label="非特邀派单触发时间(s):">
        <el-input-number v-model="form.commonDispatchOrderTriggerTime" :controls="false" />
      </el-form-item>

      <el-form-item label="非特邀抢单触发时间(s):">
        <el-input-number v-model="form.commonGrabOrderTriggerTime" :controls="false" />
      </el-form-item>
    </el-form>

    <div style="margin-top: 30px" v-hasPermi="['consultation:dispatchRule:update']">
      <el-button v-if="!edit" type="primary" @click="edit = true">编辑</el-button>
      <el-button v-if="edit" @click="cancel">取消</el-button>
      <el-button v-if="edit" type="primary" @click="submitForm">保存</el-button>
    </div>
  </div>
</template>

<script lang="ts" setup name="withdrawRule">
import { getDispatchRule, saveDispatchRule } from '@/api/consultation'
const message = useMessage() // 消息弹窗
const edit = ref(false)
const scoreType = [
  {type: 'CONSULTATION',label: '会诊评分'},
  {type: 'CURATIVE_EFFECT',label: '疗效评分'},
  {type: 'PROFESSIONAL',label: '职称评分'},
  {type: 'ACK',label: '应答评分'},
]
const specificRules = [
  { label: '评分: 评分之和/会诊数', doctorScoreDetail: 'CONSULTATION_PW' },
  { label: '未随访：', doctorScoreDetail: 'CURATIVE_EFFECT_WSF' },
        { label: '随访结果-治愈: ', doctorScoreDetail: 'CURATIVE_EFFECT_JY' },
        { label: '随访结果-好转：', doctorScoreDetail: 'CURATIVE_EFFECT_HZ' },
  { label: '随访结果-无效：', doctorScoreDetail: 'CURATIVE_EFFECT_WX' },
        { label: '主任医师：', doctorScoreDetail: 'PROFESSIONAL_ZR' },
        { label: '副主任医师：', doctorScoreDetail: 'PROFESSIONAL_FZR' },
        { label: '主治医师：', doctorScoreDetail: 'PROFESSIONAL_ZJ' },
  { label: '医师：', doctorScoreDetail: 'PROFESSIONAL_YS' },
        { label: '接听：', doctorScoreDetail: 'ACK_JT' },
        { label: '未接听：', doctorScoreDetail: 'ACK_WJT' },
        { label: '拒接：', doctorScoreDetail: 'ACK_JJ' }
]
const form = ref({
  consultationStartTime: '',
  consultationEndTime: '',
  batchDispatchCount: 3,
  callTimeOut: 30,
  consultationInterval: 0,
  grabOrderTriggerTime: 0,
  commonDispatchOrderTriggerTime: 0,
  commonGrabOrderTriggerTime: 0,
  dispatchRules: [
    {
      scoreType: 'CONSULTATION',
      remark: ['评估医生会诊（1对1）服务水平，由患者对会诊进行评价打分（1~5），未打分则为0分'],
      initScore: 0,
      specificRules: [
        {doctorScoreDetail: 'CONSULTATION_PW', value: 0}
      ],
      weight: 0,
      status: 0
    },
    {
      scoreType: 'CURATIVE_EFFECT',
      remark: [
        '评估医生会诊（1对1）服务水平，由患者对会诊进行评价打分（1~5），未打分则为0分',
        '1、最低分不限，扣分到0后，继续扣分则为负分',
        '2、最高分100，加分超过100，仍然为100分'
      ],
      initScore: 0,
      specificRules: [
        { doctorScoreDetail: 'CURATIVE_EFFECT_WSF', value: 0 },
        { doctorScoreDetail: 'CURATIVE_EFFECT_JY', value: 0 },
        { doctorScoreDetail: 'CURATIVE_EFFECT_HZ', value: 0 },
        { doctorScoreDetail: 'CURATIVE_EFFECT_WX', value: 0 }
      ],
      weight: 0,
      status: 0
    },
    {
      scoreType: 'PROFESSIONAL',
      remark: ['评估医生执业水平，依据其职称等级评分'],
      initScore: 0,
      specificRules: [
        { doctorScoreDetail: 'PROFESSIONAL_ZR', value: 0 },
        { doctorScoreDetail: 'PROFESSIONAL_FZR', value: 0 },
        { doctorScoreDetail: 'PROFESSIONAL_ZJ', value: 0 },
        { doctorScoreDetail: 'PROFESSIONAL_YS', value: 0 }
      ],
      weight: 0,
      status: 0
    },
    {
      scoreType: 'ACK',
      remark: [
        '对医生会诊应答结果进行计分，每个医生初始0分',
        '1、最低分不限，扣分到0后，继续扣分则为负分',
        '2、最高分100，加分超过100，仍然为100分'
      ],
      initScore: 0,
      specificRules: [
        { doctorScoreDetail: 'ACK_JT', value: 0 },
        { doctorScoreDetail: 'ACK_WJT', value: 0 },
        { doctorScoreDetail: 'ACK_JJ', value: 0 }
      ],
      weight: 0,
      status: 0
    },
  ]
})

onMounted(() => {
  getDetail()
})
const cancel = () => {
  getDetail()
  edit.value = false
}
const getDetail = async () => {
  const detail = await getDispatchRule()
  if (detail) {
    detail.dispatchRules.forEach(v => {
      v.remark = JSON.parse(v.remark)
    })
    form.value = detail
  }
}

const submitForm = async () => {
  let num = 0
  form.value.dispatchRules.forEach(v => {
    if (v.status === 1) {
      num = num + (v.weight * 100)
    }
  })
  
  if (num !== 100) {
   return message.error('所有启用指标的权重之和需要为1')
  }
  const params = JSON.parse(JSON.stringify(form.value))
  params.dispatchRules.forEach(v => {
    v.remark = JSON.stringify(v.remark)
  })
  saveDispatchRule(params).then(()=>{message.success('保存派单规则成功')})
  edit.value = false
}
</script>

<style scoped lang="scss">
.withdrawRule {
  width: 100%;
  height: 100%;
  padding: 20px;
  background-color: #fff;
  :deep(.el-form-item) {
    --font-size: 20px;
  }
  :deep(.el-input.is-disabled .el-input__inner) {
    -webkit-text-fill-color: #333;
  }
}
</style>
