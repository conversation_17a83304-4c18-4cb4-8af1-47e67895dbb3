import request from '@/config/axios'

export interface MessageVO {
  id: number
  msgOpe: number
  msgFrom: string
  msgTo: string
  msgBody: string
  msgDesc: string
  msgAttach: string
  msgId: number
  msgFromNick: string
  msgType: string
  msgCustomType: string
  msgExt: string
  msgTimestamp: number
  msgDevice: string
  msgClient: string
  msgClientId: string
  msgSid: string
}

export interface MessagePageReqVO extends PageParam {
  msgOpe?: number
  msgFrom?: string
  msgTo?: string
  msgBody?: string
  msgDesc?: string
  msgAttach?: string
  msgId?: number
  createTime?: Date[]
  msgFromNick?: string
  msgType?: string
  msgCustomType?: string
  msgExt?: string
  msgTimestamp?: number
  msgDevice?: string
  msgClient?: string
  msgClientId?: string
  msgSid?: string
}

export interface MessageExcelReqVO {
  msgOpe?: number
  msgFrom?: string
  msgTo?: string
  msgBody?: string
  msgDesc?: string
  msgAttach?: string
  msgId?: number
  createTime?: Date[]
  msgFromNick?: string
  msgType?: string
  msgCustomType?: string
  msgExt?: string
  msgTimestamp?: number
  msgDevice?: string
  msgClient?: string
  msgClientId?: string
  msgSid?: string
}

// 查询聊天消息存储列表
export const getMessagePageApi = async (params: MessagePageReqVO) => {
  return await request.get({ url: '/nim/admin/message/page', params })
}

// 查询聊天消息存储详情
export const getMessageApi = async (id: number) => {
  return await request.get({ url: '/nim/admin/message/get?id=' + id })
}

// 新增聊天消息存储
export const createMessageApi = async (data: MessageVO) => {
  return await request.post({ url: '/nim/admin/message/create', data })
}

// 修改聊天消息存储
export const updateMessageApi = async (data: MessageVO) => {
  return await request.put({ url: '/nim/admin/message/update', data })
}

// 删除聊天消息存储
export const deleteMessageApi = async (id: number) => {
  return await request.delete({ url: '/nim/admin/message/delete?id=' + id })
}

// 导出聊天消息存储 Excel
export const exportMessageApi = async (params: MessageExcelReqVO) => {
  return await request.download({ url: '/nim/admin/message/export-excel', params })
}

// 根据sid获得聊天消息存储列表
export const getMessageListBySid = async (sid: string) => {
  return await request.get({ url: `/nim/admin/message/find-list-by-sid?sid=${sid}`, sid })
}
