import request from '@/config/axios'

export interface DrugTempVO {
  id: number
  content: string
  taboo: string
  doctorId: number
  prescriptionId: number
  drugId: number
  frequencyId: number
  frequencyText: string
  quantityText: string
  methodId: number
  methodText: string
}

export interface DrugTempPageReqVO extends PageParam {
  doctorId?: number
  prescriptionId?: number
}

export interface DrugTempExcelReqVO {
  doctorId?: number
  prescriptionId?: number
}

// 查询医生药品模板列表
export const getDrugTempPageApi = async (params: DrugTempPageReqVO) => {
  return await request.get({ url: '/doctor/drug-temp/page', params })
}

// 查询医生药品模板详情
export const getDrugTempApi = async (id: number) => {
  return await request.get({ url: '/doctor/drug-temp/get?id=' + id })
}

// 新增医生药品模板
export const createDrugTempApi = async (data: DrugTempVO) => {
  return await request.post({ url: '/doctor/drug-temp/create', data })
}

// 修改医生药品模板
export const updateDrugTempApi = async (data: DrugTempVO) => {
  return await request.put({ url: '/doctor/drug-temp/update', data })
}

// 删除医生药品模板
export const deleteDrugTempApi = async (id: number) => {
  return await request.delete({ url: '/doctor/drug-temp/delete?id=' + id })
}

// 导出医生药品模板 Excel
export const exportDrugTempApi = async (params: DrugTempExcelReqVO) => {
  return await request.download({ url: '/doctor/drug-temp/export-excel', params })
}

// 根据处方模版id集合查询药品详细信息
export const getDrugTempApiByPrescriptionId = async (prescriptionId: number) => {
  return await request.get({
    url: `/doctor/drug-temp/find-by-prescriptionid?prescriptionId=${prescriptionId}`
  })
}
