import request from '@/config/axios'

// 查询特邀专家信息列表
export const getUserPageApi = async (params) => {
  return await request.get({ url: '/doctor/invited-expert/page', params })
}

// 修改特邀专家信息
export const updateUserApi = async (data) => {
  return await request.put({ url: '/doctor/invited-expert/update', data })
}

// 审核特邀专家
export const approvedUserApi = async (data) => {
  return await request.put({ url: '/doctor/invited-expert/approved', data })
}

// 导出特邀专家信息 Excel
export const exportUserApi = async (params) => {
  return await request.download({ url: '/doctor/invited-expert/export-excel', params })
}

// 医师审核
export const updateAuthAuthenticatedApi = async (data) => {
  return await request.put({ url: '/doctor/doctor/invited-expert/approved', data })
}
