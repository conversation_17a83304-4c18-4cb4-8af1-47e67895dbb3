import request from '@/config/axios'

// 查询慢性病药品信息列表
export const getChronicDrugPageApi = async (params) => {
  return await request.get({ url: '/operate/chronic-drug/page', params })
}

// 新增慢性病药品信息
export const createChronicDrugApi = async (data) => {
  return await request.post({ url: '/operate/chronic-drug/create', data })
}

// 更新慢性病药品信息
export const updateChronicDrugApi = async (data) => {
  return await request.put({ url: '/operate/chronic-drug/update', data })
}

// 删除慢性病药品信息
export const deleteChronicDrugApi = async (id: number) => {
  return await request.delete({ url: '/operate/chronic-drug/delete?id=' + id })
}
