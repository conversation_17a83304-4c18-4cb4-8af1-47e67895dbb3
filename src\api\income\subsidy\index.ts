import request from '@/config/axios'

// 查询补贴列表
export const getManualAwardPageApi = async (params) => {
  return await request.get({ url: '/operate/manual-award/page', params })
}

// 查询补贴详情
export const getManualAwardApi = async (id: number) => {
  return await request.get({ url: '/operate/manual-award/get?id=' + id })
}

// 选择被补贴人
export const selectUserManualAwardApi = async (phone: number) => {
  return await request.get({ url: '/operate/manual-award/selectUser?phone=' + phone })
}

// 创建线下发放补贴
export const createManualAwardApi = async (data) => {
  return await request.post({ url: '/operate/manual-award/create', data })
}

// 更新线下发放补贴
export const updateManualAwardApi = async (data) => {
  return await request.put({ url: '/operate/manual-award/update', data })
}

// 删除补贴
export const deleteManualAwardApi = async (id: number) => {
  return await request.delete({ url: '/operate/manual-award/delete?id=' + id })
}

// 发放补贴
export const distributeManualAwardApi = async (id: number) => {
  return await request.get({ url: '/operate/manual-award/distribute?id=' + id })
}

// 撤回补贴
export const rollbackManualAwardApi = async (id: number) => {
  return await request.get({ url: '/operate/manual-award/rollback?id=' + id })
}
