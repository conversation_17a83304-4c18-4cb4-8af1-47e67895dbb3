import request from '@/config/axios'
// import qs from 'qs'

export interface VisitVO {
  id: number
  orderNum: string
  content: string
  patientId: number
  sickId: number
  doctorId: number
  sickAge: number
  visitType: number
  illnessDuration: number
  illnessUrlType: number
  price: number
  isPrescription: number
  status: number
}

export interface VisitPageReqVO extends PageParam {
  createTime?: Date[]
  patientId?: number
  sickId?: number
  doctorId?: number
  visitType?: number
}

export interface VisitExcelReqVO {
  createTime?: Date[]
  patientId?: number
  sickId?: number
  doctorId?: number
  visitType?: number
}

// 查询问诊信息列表
export const getVisitPageApi = async (params: VisitPageReqVO) => {
  return await request.get({ url: '/patient/visit/page', params })
}

// 查询问诊信息列表
export const getVisitPagSuperviseApi = async (params: VisitPageReqVO) => {
  return await request.get({ url: '/patient/visit/page-supervise', params })
}

// 查询问诊信息详情
export const getVisitApi = async (id: number) => {
  return await request.get({ url: '/patient/visit/get?id=' + id })
}

// 查询问诊信息详情 V2版本
export const getVisitV2Api = async (id: number) => {
  return await request.get({ url: '/patient/visit/v2/get?id=' + id })
}

// 新增问诊信息
export const createVisitApi = async (data: VisitVO) => {
  return await request.post({ url: '/patient/visit/create', data })
}

// 修改问诊信息
export const updateVisitApi = async (data: VisitVO) => {
  return await request.put({ url: '/patient/visit/update', data })
}

// 对接-撤销对接
export const updateSuperviseApi = async (data) => {
  return await request.put({ url: '/patient/visit/update-supervise-status', data })
}

// 删除问诊信息
export const deleteVisitApi = async (id: number) => {
  return await request.delete({ url: '/patient/visit/delete?id=' + id })
}

// 导出问诊信息 Excel
export const exportVisitApi = async (params: VisitExcelReqVO) => {
  return await request.download({ url: '/patient/visit/export-excel', params })
}


// 导出问诊信息 Excel
export const finishVisitApi = async (sid: string) => {
  return await request.post({
    url: '/patient/visit/finish-visit',
    method: 'put',
    data: { sid: sid },
    headersType: 'application/x-www-form-urlencoded'
  })
}
