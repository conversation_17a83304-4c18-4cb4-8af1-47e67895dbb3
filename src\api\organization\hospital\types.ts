export type HospitalVO = {
  id: number
  sort: number
  name: string
  typeId: number
  nature: number
  rankLevel: number
  about: string
  provinceCode: number
  cityCode: number
  districtCode: number
  code: string
  shortName: string
  address: string
  regNum: string
  socialCode: string
  hospitalType: number
  phone: string
  longitude: object
  latitude: object
}

export type HospitalPageReqVO = {
  createTime: string
  name: string
  typeId: number
  nature: number
  rankLevel: number
  about: string
  provinceCode: number
  cityCode: number
  districtCode: number
  code: string
  shortName: string
  address: string
  regNum: string
  socialCode: string
  hospitalType: number
  phone: string
  longitude: object
  latitude: object
}

export type HospitalExcelReqVO = {
  createTime: string
  name: string
  typeId: number
  nature: number
  rankLevel: number
  about: string
  provinceCode: number
  cityCode: number
  districtCode: number
  code: string
  shortName: string
  address: string
  regNum: string
  socialCode: string
  hospitalType: number
  phone: string
  longitude: object
  latitude: object
}