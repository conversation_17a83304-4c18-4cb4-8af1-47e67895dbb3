import request from '@/config/axios'

export interface WithdrawRecordVO {}

export interface WithdrawRecordPageReqVO extends PageParam {
  name?: string
  phone?: string
  bankAccount?: string
  userObjType?: number
  bankType?: number
  status?: number
}

export interface WithdrawRecordExcelReqVO {
  name?: string
  phone?: string
  bankAccount?: string
  userObjType?: number
  bankType?: number
  status?: number
}

// 查询提现记录列表
export const getWithdrawRecordPageApi = async (params: WithdrawRecordPageReqVO) => {
  return await request.get({ url: '/operate/withdraw-record/page', params })
}

// 查询提现记录详情
export const getWithdrawRecordApi = async (id: number) => {
  return await request.get({ url: '/operate/withdraw-record/get?id=' + id })
}

// 新增提现记录
export const createWithdrawRecordApi = async (data: WithdrawRecordVO) => {
  return await request.post({ url: '/operate/withdraw-record/create', data })
}

// 修改提现记录
export const updateWithdrawRecordApi = async (data: WithdrawRecordVO) => {
  return await request.put({ url: '/operate/withdraw-record/update', data })
}

// 删除提现记录
export const deleteWithdrawRecordApi = async (id: number) => {
  return await request.delete({ url: '/operate/withdraw-record/delete?id=' + id })
}

// 导出提现记录 Excel
export const exportWithdrawRecordApi = async (params: WithdrawRecordExcelReqVO) => {
  return await request.download({ url: '/operate/withdraw-record/export-excel', params })
}

// 审核
export const updateWithdrawApproveApi = async (data: WithdrawRecordVO) => {
  return await request.put({ url: '/operate/withdraw-record/approve', data })
}

// 驳回
export const updateWithdrawRejectApi = async (data: WithdrawRecordVO) => {
  return await request.put({ url: '/operate/withdraw-record/reject', data })
}

// 提现
export const updateWithdrawPaymentApi = async (data: WithdrawRecordVO) => {
  return await request.put({ url: '/operate/withdraw-record/payment', data })
}

// 提现明细
export const doctorWithdrawDetailApi = async (id) => {
  return await request.get({ url: '/doctor/income-pay/withdraw-detail?withdrawRecordId=' + id })
}

// 提现明细
export const gpWithdrawDetailApi = async (id) => {
  return await request.get({ url: '/gp/income-pay/withdraw-detail?withdrawRecordId=' + id })
}
// 导出提现明细 Excel
export const exportWithdrawDetailApi = async (id) => {
  return await request.download({
    url: '/doctor/income-pay/export-withdraw-detail-excel?withdrawRecordId=' + id
  })
}
