import request from '@/config/axios'

export interface MdtTeamVO {
  id: number
}

export interface MdtTeamPageReqVO extends PageParam {
  mdtId?: number
  doctorId?: number
}

export interface MdtTeamExcelReqVO {
  mdtId?: number
  doctorId?: number
}

// 查询多学科会诊团队列表
export const getMdtTeamPageApi = async (params: MdtTeamPageReqVO) => {
  return await request.get({ url: '/doctor/mdt-team/page', params })
}

// 查询多学科会诊团队详情
export const getMdtTeamApi = async (id: number) => {
  return await request.get({ url: '/doctor/mdt-team/get?id=' + id })
}

// 新增多学科会诊团队
export const createMdtTeamApi = async (data: MdtTeamVO) => {
  return await request.post({ url: '/doctor/mdt-team/create', data })
}

// 修改多学科会诊团队
export const updateMdtTeamApi = async (data: MdtTeamVO) => {
  return await request.put({ url: '/doctor/mdt-team/update', data })
}

// 删除多学科会诊团队
export const deleteMdtTeamApi = async (id: number) => {
  return await request.delete({ url: '/doctor/mdt-team/delete?id=' + id })
}

// 导出多学科会诊团队 Excel
export const exportMdtTeamApi = async (params: MdtTeamExcelReqVO) => {
  return await request.download({ url: '/doctor/mdt-team/export-excel', params })
}

// 根据MDT ID获取参与会诊的医生
export const getMdtTeamByIds = async (ids: Array<number>) => {
    if (ids.length <= 0) return []
    return await request.get({ url: '/doctor/mdt-team/find-doctors-by-mdtids?ids=' + ids })
  }
  



