import request from '@/config/axios'

export interface AwardTypeVO {
  id: number
  awardName: string
  awardCode: string
}

export interface AwardTypePageReqVO extends PageParam {
  awardName?: string
  awardCode?: string
  createTime?: Date[]
}

export interface AwardTypeExcelReqVO {
  awardName?: string
  awardCode?: string
  createTime?: Date[]
}

// 查询任务奖励配置列表
export const getAwardTypePageApi = async (params: AwardTypePageReqVO) => {
  return await request.get({ url: '/mission/award-type/page', params })
}
export const getAllAwatdTypeListApi = async () => {
  return await request.get({ url: '/mission/award-type/find-all-list' })
}


// 查询任务奖励配置详情
export const getAwardTypeApi = async (id: number) => {
  return await request.get({ url: '/mission/award-type/get?id=' + id })
}

// 新增任务奖励配置
export const createAwardTypeApi = async (data: AwardTypeVO) => {
  return await request.post({ url: '/mission/award-type/create', data })
}

// 修改任务奖励配置
export const updateAwardTypeApi = async (data: AwardTypeVO) => {
  return await request.put({ url: '/mission/award-type/update', data })
}

// 删除任务奖励配置
export const deleteAwardTypeApi = async (id: number) => {
  return await request.delete({ url: '/mission/award-type/delete?id=' + id })
}

// 导出任务奖励配置 Excel
export const exportAwardTypeApi = async (params: AwardTypeExcelReqVO) => {
  return await request.download({ url: '/mission/award-type/export-excel', params })
}

// 获得所有可用的任务奖励类型
export const getAwardTypeListApi=async (ids:Array<number>)=>{
    if(ids.length<=0) return []
    return await request.get({url:'/mission/award-type/list?ids='+ids})
}
