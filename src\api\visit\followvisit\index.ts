import request from '@/config/axios'

export interface FollowVisitVO {
  id: number
  followDoctorType: number
  followDoctorId: number
  sickId: number
  visitType: number
  visitId: number
  relevanceDoctorId: number
  therapeuticEffect: number
  compliance: number
  adverseReaction: number
  content: string
}

export interface FollowVisitPageReqVO extends PageParam {
  followDoctorType?: number
  followDoctorId?: number
  sickId?: number
  visitType?: number
}

export interface FollowVisitExcelReqVO {
  followDoctorType?: number
  followDoctorId?: number
  sickId?: number
  visitType?: number
}

// 查询随访记录列表
export const getFollowVisitPageApi = async (params: FollowVisitPageReqVO) => {
  return await request.get({ url: '/patient/follow-visit/page', params })
}

// 查询随访记录详情
export const getFollowVisitApi = async (id: number) => {
  return await request.get({ url: '/patient/follow-visit/get?id=' + id })
}

// 新增随访记录
export const createFollowVisitApi = async (data: FollowVisitVO) => {
  return await request.post({ url: '/patient/follow-visit/create', data })
}

// 审核随访记录
export const auditFollowVisitApi = async (data) => {
  return await request.post({ url: '/patient/follow-visit/audit', data })
}

// 修改随访记录
export const updateFollowVisitApi = async (data: FollowVisitVO) => {
  return await request.put({ url: '/patient/follow-visit/update', data })
}

// 删除随访记录
export const deleteFollowVisitApi = async (id: number) => {
  return await request.delete({ url: '/patient/follow-visit/delete?id=' + id })
}

// 导出随访记录 Excel
export const exportFollowVisitApi = async (params: FollowVisitExcelReqVO) => {
  return await request.download({ url: '/patient/follow-visit/export-excel', params })
}

// 查询随访记录详情
export const getFollowVisitByIdTypeApi = async (visitId: number, visitType: number) => {
  return await request.get({
    url: `/patient/follow-visit/list-visit?visitId=${visitId}&visitType=${visitType}`
  })
}

// 查询处方详情
export const getSuggestDetailApi = async (suggestId) => {
  return await request.get({
    url: `/doctor/prescription-api/find-detail?prescriptionId=${suggestId}`,
    base: import.meta.env.VITE_BASE_URL + '/web-api'
  })
}
