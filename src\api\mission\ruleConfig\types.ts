export type RuleConfigVO = {
  id: number
  ruleName: string
  ruleCode: string
  ruleCompleteConfig: string
  ruleAwardType: number
  ruleRepetition: boolean
}

export type RuleConfigPageReqVO = {
  ruleName: string
  ruleCompleteConfig: string
  ruleAwardType: number
  ruleRepetition: boolean
  ruleCode: string
  createTime: number
}

export type RuleConfigExcelReqVO = {
  ruleName: string
  ruleCompleteConfig: string
  ruleAwardType: number
  ruleRepetition: boolean
  ruleCode: string
  createTime: number
}