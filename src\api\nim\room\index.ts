import request from '@/config/axios'

export interface RoomVO {
  id: number
  orderId: number
  orderNo: string
  orderType: number
  accountId: number
  templateId: number
  roomArchiveId: string
  roomUuid: string
  roomName: string
  roomPassword: string
  roomWhiteboard: boolean
  roomChatroom: boolean
  roomLive: boolean
  roomRtc: boolean
  roomSip: boolean
  roomRecord: boolean
  roomSeat: boolean
}

export interface RoomPageReqVO extends PageParam {
  orderId?: number
  orderNo?: string
  orderType?: number
  accountId?: number
  templateId?: number
  roomArchiveId?: string
  roomUuid?: string
  roomName?: string
  createTime?: Date[]
}

export interface RoomExcelReqVO {
  orderId?: number
  orderNo?: string
  orderType?: number
  accountId?: number
  templateId?: number
  roomArchiveId?: string
  roomUuid?: string
  roomName?: string
  createTime?: Date[]
}

// 查询音视频账号谁创建的房间列表
export const getRoomPageApi = async (params: RoomPageReqVO) => {
  return await request.get({ url: '/nim/room/page', params })
}

// 查询音视频账号谁创建的房间详情
export const getRoomApi = async (id: number) => {
  return await request.get({ url: '/nim/room/get?id=' + id })
}

// 新增音视频账号谁创建的房间
export const createRoomApi = async (data: RoomVO) => {
  return await request.post({ url: '/nim/room/create', data })
}

// 修改音视频账号谁创建的房间
export const updateRoomApi = async (data: RoomVO) => {
  return await request.put({ url: '/nim/room/update', data })
}

// 删除音视频账号谁创建的房间
export const deleteRoomApi = async (id: number) => {
  return await request.delete({ url: '/nim/room/delete?id=' + id })
}

// 导出音视频账号谁创建的房间 Excel
export const exportRoomApi = async (params: RoomExcelReqVO) => {
  return await request.download({ url: '/nim/room/export-excel', params })
}

// 获得房间列表信息
export const getRoomListApi= async ()=>{
  return await request.get({url: '/nim/room/find-select-list'});
}
