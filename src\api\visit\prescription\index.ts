import request from '@/config/axios'

export interface PrescriptionVO {
  id: number
  orderNum: string
  prescriptionNum: string
  age: number
  checkTime: Date
  checkContent: string
  patientId: number
  sickId: number
  doctorId: number
  visitId: number
  visitType: number
  departCode: string
  medicalCertificate: string
  diagnoseSuggest: string
  doctorSealUrl: string
  hosSealUrl: string
  checkPharmacistId: number
  status: number,
  rxType?: number,
  rxNum?: string,
  rxId?: number,
  rxName?: string,
  checkDrugList?: Array<shopReq>,
}
 interface shopReq  {
      drugId: number,
      num: string,
      name: string,
      inventory:number,
      unitPrice: number
  }

export interface PrescriptionPageReqVO extends PageParam {
  createTime?: Date[]
  patientId?: number
  sickId?: number
  doctorId?: number
  visitType?: number
}

export interface PrescriptionExcelReqVO {
  createTime?: Date[]
  patientId?: number
  sickId?: number
  doctorId?: number
  visitType?: number
}

// 查询患者处方信息列表
export const getPrescriptionPageApi = async (params: PrescriptionPageReqVO) => {
  return await request.get({ url: '/patient/prescription/page', params })
}

// 查询患者处方信息详情
export const getPrescriptionApi = async (id: number) => {
  return await request.get({ url: '/patient/prescription/get?id=' + id })
}

// 新增患者处方信息
export const createPrescriptionApi = async (data: PrescriptionVO) => {
  return await request.post({ url: '/patient/prescription/create', data })
}

// 修改患者处方信息
export const updatePrescriptionApi = async (data: PrescriptionVO) => {
  return await request.put({ url: '/patient/prescription/update', data })
}

// 删除患者处方信息
export const deletePrescriptionApi = async (id: number) => {
  return await request.delete({ url: '/patient/prescription/delete?id=' + id })
}

// 导出患者处方信息 Excel
export const exportPrescriptionApi = async (params: PrescriptionExcelReqVO) => {
  return await request.download({ url: '/patient/prescription/export-excel', params })
}

// 锁定处方

export const lockingPrescriptionApi = async (data: PrescriptionVO) => {
  return await request.put({ url: '/patient/prescription/Locking', data })
  return await request.put({ url: '/patient/prescription/Locking', data })
}

//处方审核通过
export const ApprovedPrescriptionApi = async (data: PrescriptionVO) => {
  return await request.put({ url: '/patient/prescription/Approved', data })
  return await request.put({ url: '/patient/prescription/Approved', data })
}


//处方审核驳回
export const RejectedPrescriptionApi = async (data: PrescriptionVO) => {
  return await request.put({ url: '/patient/prescription//Rejected', data })
}

//根据问诊订单的编号查询处方信息
export const getPrescriptionByVisitId = async (visitIds: Array<number>) => {
  if (visitIds.length <= 0) return []
  return await request.get({ url: '/patient/prescription/find-prescription-by-visitIds?visitIds=' + visitIds })
}
// 查询患者处方信息信息集合
export const getPrescriptionGetDetailApi = async (id: number) => {
  return await request.get({ url: '/patient/prescription/get-detail?id=' + id })
}
//分页获取处方药品匹配药店列表
export const getPrescriptionRxPageApi = async (params: any) => {
  return await request.get({ url: '/patient/prescription/page-prescription-rx',params})
}



