<template>
  <ContentWrap>
    <!-- 列表 -->
    <vxe-grid ref="xGrid" v-bind="gridOptions" class="xtable-scrollbar">
      <template #toolbar_buttons>
        <!-- 操作：新增 -->
        <XButton
          type="primary"
          preIcon="ep:zoom-in"
          :title="t('action.add')"
          v-hasPermi="['visit:telemedicine:create']"
          @click="handleCreate()"
        />
        <!-- 操作：导出 -->
        <XButton
          type="warning"
          preIcon="ep:download"
          :title="t('action.export')"
          v-hasPermi="['doctor:telemedicine:export']"
          @click="handleExport()"
        />
      </template>

      <template #hospitalId>
        <div>
          <ScrollSelect
            v-model:value="queryParams.hospitalId"
            :request="getHospitalPageApi"
            dictLabel="name"
            dictValue="id"
            searchKey="name" 
            :params="{pageNo: 1,pageSize: 100}"
          />
        </div>
      </template>

      <template #actionbtns_default="{ row }">
        <XTextButton
          v-show="row.isFollowUp === 0 && row.visitStatus === 2"
          title="发起随访"
          v-hasPermi="['patient:follow-visit:create']"
          @click="handleCreateFollowVisit(row.id)"
        />
        <!-- 操作：修改 -->
        <XTextButton
          preIcon="ep:edit"
          :title="t('action.edit')"
          v-hasPermi="['visit:telemedicine:update']"
          @click="handleUpdate(row.id)"
        />
        <!-- 操作：详情 -->
        <XTextButton
          preIcon="ep:view"
          :title="t('action.detail')"
          v-hasPermi="['visit:telemedicine:query']"
          @click="handleDetail(row)"
        />
        <!-- 操作：删除 -->
        <XTextButton
          preIcon="ep:delete"
          :title="t('action.del')"
          v-hasPermi="['visit:telemedicine:delete']"
          @click="handleDelete(row.id)"
        />
        <XTextButton
          preIcon="ep:view"
          title="会诊详情"
          v-hasPermi="['visit:telemedicine:consultation-detail']"
          @click="handleVisitDetail(row)"
        />
        <XTextButton
          preIcon="ep:view"
          title="患者详情"
          v-hasPermi="['visit:telemedicine:sick-detail']"
          @click="handleSickDetail(row)"
        />
        <XTextButton
          v-show="row.auditStatus === 0 && row.visitStatus === 2"
          title="审核"
          v-hasPermi="['visit:telemedicine:audit']"
          @click="handleAudit(row)"
        />
      </template>
      <template #gpAreaCode="{ row }">
        <el-tag v-if="!row.gpAreaCodeName" type="danger">不存在</el-tag>
        <el-tag v-else type="success"> {{ row.gpAreaCodeName }} </el-tag>
      </template>
    </vxe-grid>
  </ContentWrap>
  <!-- 弹窗 -->
  <XModal id="telemedicineModel" :loading="modelLoading" v-model="modelVisible" :title="modelTitle">
    <!-- 表单：添加/修改 -->
    <Form
      ref="formRef"
      v-if="['发起随访'].includes(actionType)"
      :schema="allSchemas?.formSchema"
      :rules="rules"
      :isCol="false"
    />
    <!-- 表单：详情 -->
    <Descriptions
      v-if="actionType === 'detail'"
      :schema="allSchemas.detailSchema"
      :data="detailData"
    />

    <MenuTab
      v-if="actionType === 'sickDetail'"
      :sickId="detailSickId"
      :default-descriptions="{
        schema: userSick.allSchemas.detailSchema,
        data: detailData
      }"
    />

    <VisitDetail v-if="actionType === 'visitdetail'" :id="detailId" :sid="sid"/>

    <div v-if="actionType === '审核'" class="audit-form">
      <div class="radio-group">
        <div class="radio-item">
          <span class="label">发起医生审核:</span>
          <el-radio-group v-model="auditData.gpAuditStatus">
            <el-radio :label="1" :disabled="currentRow?.gpAuditStatus !== 0">通过</el-radio>
            <el-radio :label="2" :disabled="currentRow?.gpAuditStatus !== 0">不通过</el-radio>
          </el-radio-group>
        </div>
        <div class="radio-item">
          <span class="label">专家审核:</span>
          <el-radio-group v-model="auditData.auditStatus">
            <el-radio :label="1" :disabled="currentRow?.auditStatus !== 0">通过</el-radio>
            <el-radio :label="2" :disabled="currentRow?.auditStatus !== 0">不通过</el-radio>
          </el-radio-group>
        </div>
      </div>
      <div class="input-area">
        <span class="label">审核意见:</span>
        <el-input
          v-model="auditData.auditContent"
          type="textarea"
          :autosize="{ minRows: 4, maxRows: 8 }"
          maxlength="300"
          show-word-limit
          placeholder="请输入审核意见(任意一项不通过时必填)"
        />
      </div>
    </div>

    <template #footer>
      <!-- 按钮：保存 -->
      <XButton
        v-if="['发起随访'].includes(actionType)"
        type="primary"
        :title="t('action.save')"
        :loading="actionLoading"
        @click="submitForm()"
      />
      <!-- 按钮：关闭 -->
      <XButton :loading="actionLoading" :title="t('dialog.close')" @click="modelVisible = false" />
      <XButton v-if="actionType === '审核'" type="primary" :loading="actionLoading" title="提交" @click="submitAudit()" />
    </template>
  </XModal>
</template>
<script setup lang="ts" name="Telemedicine">
// 全局相关的 import
import { ref, unref, reactive } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useVxeGrid } from '@/hooks/web/useVxeGrid'
import { VxeGridInstance } from 'vxe-table'
import { FormExpose } from '@/components/Form'
// 业务相关的 import
import { rules, allSchemas } from './telemedicine.data'
import * as TelemedicineApi from '@/api/visit/telemedicine'
import { createFollowVisitApi, FollowVisitVO } from '@/api/visit/followvisit'
import { getTelemedicinePageListApi, pageInit } from '.'
//@ts-ignore
import VisitDetail from './components/visitdetail/index.vue'
import { MenuTab } from '@/views/user/userSick/components/index'
import * as userSick from '@/views/user/userSick/userSick.data'
import { getUserSickApi } from '@/api/user/userSick'
import { getHospitalPageApi } from '@/api/organization/hospital'
import ScrollSelect from '@/components/scrollSelect/index.vue'

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

// 列表相关的变量
const queryParams = reactive({ hospitalId: ''})
const xGrid = ref<VxeGridInstance>() // 列表 Grid Ref
const { gridOptions, getList, deleteData, getSearchData } = useVxeGrid<TelemedicineApi.TelemedicineVO>(
  {
    allSchemas: allSchemas,
    getListApi: getTelemedicinePageListApi,
    deleteApi: TelemedicineApi.deleteTelemedicineApi,
    queryParams: queryParams
  }
)

// 弹窗相关的变量
const modelVisible = ref(false) // 是否显示弹出层
const modelTitle = ref('edit') // 弹出层标题
const modelLoading = ref(false) // 弹出层loading
const actionType = ref('') // 操作按钮的类型
const actionLoading = ref(false) // 按钮 Loading
const formRef = ref<FormExpose>() // 表单 Ref
const detailData = ref() // 详情 Ref
const detailSickId = ref() // 患者id
const auditData = reactive({
  id: 0,
  gpAuditStatus: 0,
  auditStatus: 0,
  auditContent: ''
}) // 审核参数
const currentRow = ref<any>(null) // 保存当前审核的行数据

// 设置标题
const setDialogTile = (type: string) => {
  modelLoading.value = true
  modelTitle.value = t('action.' + type)
  actionType.value = type
  modelVisible.value = true
}

// 新增操作
const handleCreate = () => {
  setDialogTile('create')
  modelLoading.value = false
}

import { useUserStore } from '@/store/modules/user'
const userStore = useUserStore()
const handleCreateFollowVisit = async (rowId: number) => {
  setDialogTile('发起随访')
  modelTitle.value  = '发起随访'
  const user = await userStore.getUser
  // 设置数据
  const res = await TelemedicineApi.getTelemedicineApi(rowId)
  console.log(res);
  
  const data = {
    followDoctorType: 4,
    followDoctorId: res.doctorId,
    sickId: res.sickId,
    visitType: 4,
    visitId: res.id,
    visitTime: null,
    therapeuticEffect: null,
    compliance: null,
    adverseReaction: null,
    content: '',
    annexList: [],
    creatorId: user.id,
  }
  
  unref(formRef)?.setValues(data)
  modelLoading.value = false
}

onMounted(() => {
  pageInit()
})

// 导出操作
const handleExport = async () => {
  try {
    const searchParams = await getSearchData(xGrid)
    const res = await TelemedicineApi.exportTelemedicineAsyncApi(searchParams)
    console.log(res)
    message.success('导出请求已提交，请稍后查看基础设施-导出列表')
  } catch (error) {
    console.error('导出失败:', error)
    message.error('导出失败，请重试')
  }
}

// 修改操作
const handleUpdate = async (rowId: number) => {
  setDialogTile('update')
  // 设置数据
  const res = await TelemedicineApi.getTelemedicineApi(rowId)
  unref(formRef)?.setValues(res)
  modelLoading.value = false
}

// 详情操作
const handleDetail = async (row: any) => {
  setDialogTile('detail')
  const res = await TelemedicineApi.getTelemedicineApi(row.id)
  detailData.value = res
  detailData.value.hospitalId = row.hospitalName
  detailData.value.visitStatus = row.visitStatus
  detailData.value.sickName = row.sickName
  detailData.value.sickPhone = row.sickPhone
  detailData.value.SickSex = row.SickSex
  detailData.value.gpName = row.gpName
  detailData.value.doctorName = row.doctorName
  detailData.value.isSuggest = row.isSuggest
  detailData.value.isPrescription = row.isPrescription
  detailData.value.age = row.age
  detailData.value.gpAreaCode = row.gpAreaCodeStr

  modelLoading.value = false
}

// 删除操作
const handleDelete = async (rowId: number) => {
  await deleteData(xGrid, rowId)
}

// 提交按钮
const submitForm = async () => {
  const elForm = unref(formRef)?.getElFormRef()
  if (!elForm) return
  elForm.validate(async (valid) => {
    if (valid) {
      actionLoading.value = true
      // 提交请求
      try {
        const data = JSON.parse(JSON.stringify(unref(formRef)?.formModel as FollowVisitVO))
        data.annexList = data.annexList.map(v => {
          return v.url
        })
        await createFollowVisitApi(data)
        message.success('发起成功')
        modelVisible.value = false
      } finally {
        actionLoading.value = false
        // 刷新列表
        await getList(xGrid)
      }
    }
  })
}

const detailId = ref(0)
const sid = ref('')
const handleVisitDetail = (row: any) => {
  detailId.value = row.id
  sid.value = row.sid
  setDialogTile('visitdetail')
  modelTitle.value = '会诊详情'
  modelLoading.value = false
}

const handleSickDetail = async(row: any) => {
  detailSickId.value = row.sickId
  const res = await getUserSickApi(detailSickId.value)
  res.gpName = row.gpName
  res.gpAreaCode = row.gpAreaCodeStr
  detailData.value = res
  setDialogTile('sickDetail')
  modelTitle.value = '患者详情'
  modelLoading.value = false
}

const handleAudit = async(row: any) => {
  currentRow.value = row // 保存当前行数据
  auditData.id = row.id
  auditData.gpAuditStatus = 0
  auditData.auditStatus = 0
  auditData.auditContent = ''
  setDialogTile('审核')
  modelTitle.value = '审核'
  modelLoading.value = false
}

const submitAudit = async() => {
  // 验证是否都选择了审核类型
  if (auditData.gpAuditStatus === 0 || auditData.auditStatus === 0) {
    return message.error('村医审核和专家审核都需要选择')
  }
  
  // 当有一个审核不通过时,审核意见必填
  if ((auditData.gpAuditStatus === 2 || auditData.auditStatus === 2) && !auditData.auditContent) {
    return message.error('当审核不通过时,请填写审核意见')
  }

  // 发送审核请求
  try {
    actionLoading.value = true
    await TelemedicineApi.auditTelemedicineApi(auditData)
    message.success('审核成功')
    modelVisible.value = false
    await getList(xGrid)
  } catch (error) {
    message.error('审核失败')
  } finally {
    actionLoading.value = false
  }
}
</script>
<style scoped>
.audit-form {
  padding: 20px;
}
.radio-group {
  margin-bottom: 20px;
}
.radio-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}
.label {
  width: 80px;
  text-align: right;
  margin-right: 10px;
}
.input-area {
  display: flex;
}
.input-area .el-input {
  flex: 1;
}
</style>

