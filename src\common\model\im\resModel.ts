import { requestStatus } from'../../enum/im';

interface bodyModel<T> {
  bCode?: string;
  reqCode?: number;
  msg?: string;
  code?: number;
  data?: T;
}

class BaseModel<T> {
  data: T;
  reqCode: number;
  bCode: string;
  msg: string;
  code: number;

  constructor({
    //@ts-ignore
    data = null,
    reqCode = requestStatus.success,
    bCode = '0000000',
    msg = '',
    code = -1,
  }: bodyModel<T>) {
    this.data = data;
    this.reqCode = reqCode;
    this.bCode = bCode;
    this.msg = msg;
    this.code = code;
  }
}

/**
 * 成功的模型
 */
class SuccessModel<T> extends BaseModel<T> {
  constructor({ data, reqCode = requestStatus.success, bCode, msg, code }: bodyModel<T>) {
    super({ data, reqCode, bCode, msg, code });
  }
}

/**
 * 错误的模型
 */
class ErrorModel<T> extends BaseModel<T> {
  constructor({ data, reqCode = requestStatus.error, bCode, msg, code }: bodyModel<T>) {
    super({ data, reqCode, bCode, msg, code });
  }
}

class ReturnModel<T> extends BaseModel<T> {
  constructor({ data, reqCode, bCode, msg, code }: bodyModel<T>) {
    if (reqCode) {
      super({ data, reqCode, bCode, msg, code });
    } else {
      super({ data, reqCode: requestStatus.error, bCode, msg, code });
    }
  }
}

/** 分页子模型
 * @param { T[] } list 数据列表
 * @param { number } total 数据总量
 */
export type pageItemModel<T> = {
  list: T[];
  total: number;
};
/** 分页模型
 * @param { T[] } list 数据列表
 * @param { number } total 数据总量
 */
type pageModel<T> = bodyModel<pageItemModel<T>>;

export { SuccessModel, ErrorModel, ReturnModel, bodyModel, BaseModel, pageModel };
