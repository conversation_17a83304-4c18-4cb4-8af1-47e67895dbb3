import request from '@/config/axios'

export interface HealthCenterDispatchVO {
  id?: number
  doctorId?: number
  sort: number
}

export interface HealthCenterDispatchCreateReqVO {
  doctorId?: number
  doctorIdList?: number[]
  sort: number
}

export interface HealthCenterDispatchUpdateReqVO {
  id: number
  doctorId?: number
  sort: number
}

export interface HealthCenterDispatchPageReqVO extends PageParam {
  name?: string
  phone?: string
  hospitalId?: number
}

export interface HealthCenterDispatchRespVO {
  id: number
  sort: number
  name: string
  phone: string
  hospitalDepart: string
  hospitalName: string
  officeHolderName: string
  createTime: string
}

// 查询卫生院调度分页
export const getHealthCenterDispatchPageApi = async (params: HealthCenterDispatchPageReqVO) => {
  return await request.get({ url: '/doctor/health-center-dispatch/page', params })
}

// 查询卫生院调度详情
export const getHealthCenterDispatchApi = async (id: number) => {
  return await request.get({ url: '/doctor/health-center-dispatch/get?id=' + id })
}

// 新增卫生院调度
export const createHealthCenterDispatchApi = async (data: HealthCenterDispatchCreateReqVO) => {
  return await request.post({ url: '/doctor/health-center-dispatch/create', data })
}

// 修改卫生院调度
export const updateHealthCenterDispatchApi = async (data: HealthCenterDispatchUpdateReqVO) => {
  return await request.put({ url: '/doctor/health-center-dispatch/update', data })
}

// 删除卫生院调度
export const deleteHealthCenterDispatchApi = async (id: number) => {
  return await request.delete({ url: '/doctor/health-center-dispatch/delete?id=' + id })
} 