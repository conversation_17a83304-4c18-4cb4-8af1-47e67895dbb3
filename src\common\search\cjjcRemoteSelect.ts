import { getUserSick<PERSON>age<PERSON><PERSON> } from "@/api/user/userSick";
import cjjcRemoteSelect, { ListItem } from "@/utils/renderer/cjjcRemoteSelect";
import { getUser<PERSON>age<PERSON>pi as getDoctorUserPageApi} from '@/api/user/doctor'
import { getUserPage<PERSON>pi as getGpUserPageApi } from "@/api/user/gp";
import { getUserPageApi as getPatientUserPageApi } from "@/api/user/patient";

//远程医生文本框模式搜索
const remoteDoctorMethod = async (query: string): Promise<ListItem[]> => {

    //实现异步请求函数
    // 例如  const res = await requiest(query)
    if(query.length<=0) return []
    const res = await getDoctorUserPageApi({name: query,pageNo : 1,pageSize:20})
    const list = res.list;
    const transformedData = list.map(item => ({
      value: item.id,
      label: item.name
    }));
    return transformedData;
  }
  //远程患者文本框模式搜索
  const remoteSickMethod = async (query: string): Promise<ListItem[]> => {

    //实现异步请求函数
    // 例如  const res = await requiest(query)
    if(query.length<=0) return []
    const res = await getUserSickPageApi({name: query,pageNo : 1,pageSize:20})
    const list = res.list;
    const transformedData = list.map(item => ({
      value: item.id,
      label: item.name
    }));
    return transformedData;
  }

 //远程村医文本框模式搜索
 const remoteGpkMethod = async (query: string): Promise<ListItem[]> => {

    //实现异步请求函数
    // 例如  const res = await requiest(query)
    if(query.length<=0) return []
    const res = await getGpUserPageApi({name: query,pageNo : 1,pageSize:20})
    const list = res.list;
    const transformedData = list.map(item => ({
      value: item.id,
      label: item.name
    }));
    return transformedData;
  }

   //远程患者账号文本框模式搜索
 const remotePatientkMethod = async (query: string): Promise<ListItem[]> => {

  //实现异步请求函数
  // 例如  const res = await requiest(query)
  if(query.length<=0) return []
  const res = await getPatientUserPageApi({name: query,pageNo : 1,pageSize:20})
  const list = res.list;
  const transformedData = list.map(item => ({
    value: item.id,
    label: item.name
  }));
  return transformedData;
}

  
  export const cjjcDoctorRst = cjjcRemoteSelect(remoteDoctorMethod)
  export const cjjcSickRst = cjjcRemoteSelect(remoteSickMethod)
  export const cjjcGpRst = cjjcRemoteSelect(remoteGpkMethod)
  export const cjjcPatientRst = cjjcRemoteSelect(remotePatientkMethod)