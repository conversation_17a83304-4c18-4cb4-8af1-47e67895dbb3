import type { VxeCrudSchema } from '@/hooks/web/useVxeCrudSchemas'

// CrudSchema
const crudSchemas = reactive<VxeCrudSchema>({
  primaryKey: 'exportId',
  action: true,
  actionWidth: '200px',
  columns: [
    {
      title: '导出类型',
      field: 'type',
      isSearch: true
    },
    {
      title: '导出人',
      field: 'creator'
    },
    {
      title: '状态',
      field: 'status',
      isSearch: true,
      table: {
        slots: {
          default: 'status_default'
        }
      }
    },
    {
      title: '导出时间',
      field: 'createTime',
      formatter: 'formatDate',
      search: {
        show: true,
        itemRender: {
          name: 'XDataTimePicker'
        }
      }
    },
    {
      title: '完成时间',
      field: 'finishTime',
      formatter: 'formatDate'
    },
    {
      title: '文件地址',
      field: 'fileUrl',
      isTable: false,
      isForm: false
    }
  ]
})

export const { allSchemas } = useVxeCrudSchemas(crudSchemas)
