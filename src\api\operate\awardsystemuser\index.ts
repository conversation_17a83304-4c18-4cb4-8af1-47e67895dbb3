import request from '@/config/axios'

export interface AwardSystemUserVO {
  id: number
  groupId: number
  userType: number
  userId: number
  type: number
}

export interface AwardSystemUserPageReqVO extends PageParam {
  groupId?: number
  userType?: number
  userId?: number
  type?: number
  createTime?: Date[]
}

export interface AwardSystemUserExcelReqVO {
  groupId?: number
  userType?: number
  userId?: number
  type?: number
  createTime?: Date[]
}

// 查询任务系统分配子项列表
export const getAwardSystemUserPageApi = async (params: AwardSystemUserPageReqVO) => {
  return await request.get({ url: '/operate/award-system-user/page', params })
}

// 查询任务系统分配子项详情
export const getAwardSystemUserApi = async (id: number) => {
  return await request.get({ url: '/operate/award-system-user/get?id=' + id })
}

// 查询任务系统分配子项详情
export const getAwardSystemUserByAccountApiList = async (
  accountids: number[],
  userType: number
) => {
  return await request.get({
    url: `/operate/award-system-user/list-by-account?accountids=${accountids}&userType=${userType}`
  })
}

// 新增任务系统分配子项
export const createAwardSystemUserApi = async (data: AwardSystemUserVO) => {
  return await request.post({ url: '/operate/award-system-user/create', data })
}

// 修改任务系统分配子项
export const updateAwardSystemUserApi = async (data: AwardSystemUserVO) => {
  return await request.put({ url: '/operate/award-system-user/update', data })
}

// 删除任务系统分配子项
export const deleteAwardSystemUserApi = async (id: number) => {
  return await request.delete({ url: '/operate/award-system-user/delete?id=' + id })
}

// 导出任务系统分配子项 Excel
export const exportAwardSystemUserApi = async (params: AwardSystemUserExcelReqVO) => {
  return await request.download({ url: '/operate/award-system-user/export-excel', params })
}
