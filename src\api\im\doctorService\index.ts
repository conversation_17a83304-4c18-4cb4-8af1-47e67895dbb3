import service from '../https'
import { ErrorModel, SuccessModel } from '@/common/model/resModel'
import { reqStatusModel } from '@/common/model/im/imModel'



/** 获取问诊订单完成情况
 * @param { number } visitId visitId
 * @param { string } userid 登录用户id
 */
export const putModifyVisitStatus = (visitId: number, userid: string) =>
  service
    .put<reqStatusModel>(
      `/app-api/doctor/visit-api/modify-visit-status?visitId=${visitId}&doctorId=${userid}`
    )
    .then((response) => {
      return response as SuccessModel<reqStatusModel>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })
