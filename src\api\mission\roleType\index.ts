import request from '@/config/axios'

export interface RoleTypeVO {
  id: number
  roleName: string
  roleCode: string
}

export interface RoleTypePageReqVO extends PageParam {
  roleName?: string
  roleCode?: string
  createTime?: Date[]
}

export interface RoleTypeExcelReqVO {
  roleName?: string
  roleCode?: string
  createTime?: Date[]
}

// 查询任务参与者类型配置列表
export const getRoleTypePageApi = async (params: RoleTypePageReqVO) => {
  return await request.get({ url: '/mission/role-type/page', params })
}

export const getRoleListApi=async (ids:Array<number>)=>{
  if(ids.length<=0) return []
  return await request.get({url:'/mission/role-type/list?ids='+ids})
}

export const getAllRoleTypeListApi=async ()=>{
  return await request.get({url:'/mission/role-type/list-all'})
}

// 查询任务参与者类型配置详情
export const getRoleTypeApi = async (id: number) => {
  return await request.get({ url: '/mission/role-type/get?id=' + id })
}

// 新增任务参与者类型配置
export const createRoleTypeApi = async (data: RoleTypeVO) => {
  return await request.post({ url: '/mission/role-type/create', data })
}

// 修改任务参与者类型配置
export const updateRoleTypeApi = async (data: RoleTypeVO) => {
  return await request.put({ url: '/mission/role-type/update', data })
}

// 删除任务参与者类型配置
export const deleteRoleTypeApi = async (id: number) => {
  return await request.delete({ url: '/mission/role-type/delete?id=' + id })
}

// 导出任务参与者类型配置 Excel
export const exportRoleTypeApi = async (params: RoleTypeExcelReqVO) => {
  return await request.download({ url: '/mission/role-type/export-excel', params })
}
