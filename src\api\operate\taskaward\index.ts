import request from '@/config/axios'

export interface TaskAwardVO {
  id: number
  type: number
  userType: number
  userId: number
  taskCount: number
  unitPrice: number
  awardPrice: number
  repeatCount: number
  isRepeat: number
  awardType: number
  awardStartTime: Date
  awardEndTime: Date
  jobTime: string
  missionId: number
  status: number
  enable: number
  peopleCount?:number
}

export interface TaskAwardPageReqVO extends PageParam {
  type?: number
  userType?: number
  userId?: number
  taskCount?: number
  unitPrice?: number
  awardPrice?: number
  repeatCount?: number
  isRepeat?: number
  awardType?: number
  awardStartTime?: Date[]
  awardEndTime?: Date[]
  jobTime?: string
  createTime?: Date[]
  missionId?: number
  status?: number
  enable?: number
  peopleCount?:number
}

export interface TaskAwardExcelReqVO {
  type?: number
  userType?: number
  userId?: number
  taskCount?: number
  unitPrice?: number
  awardPrice?: number
  repeatCount?: number
  isRepeat?: number
  awardType?: number
  awardStartTime?: Date[]
  awardEndTime?: Date[]
  jobTime?: string
  createTime?: Date[]
  missionId?: number
  status?: number
  enable?: number
  peopleCount?:number
}

// 查询任务奖励列表
export const getTaskAwardPageApi = async (params: TaskAwardPageReqVO) => {
  return await request.get({ url: '/operate/task-award/page', params })
}

// 查询任务奖励详情
export const getTaskAwardApi = async (id: number) => {
  return await request.get({ url: '/operate/task-award/get?id=' + id })
}

// 新增任务奖励
export const createTaskAwardApi = async (data: TaskAwardVO) => {
  return await request.post({ url: '/operate/task-award/create', data })
}

// 修改任务奖励
export const updateTaskAwardApi = async (data: TaskAwardVO) => {
  return await request.put({ url: '/operate/task-award/update', data })
}

// 删除任务奖励
export const deleteTaskAwardApi = async (id: number) => {
  return await request.delete({ url: '/operate/task-award/delete?id=' + id })
}

export const getTaskAwardListApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/task-award/list?ids=' + ids })
}

// 导出任务奖励 Excel
export const exportTaskAwardApi = async (params: TaskAwardExcelReqVO) => {
  return await request.download({ url: '/operate/task-award/export-excel', params })
}


// 任务启用停用
export const enableTaskAwardApi = async (data: TaskAwardVO) => {
  return await request.put({ url: '/operate/task-award/enable', data })
}

