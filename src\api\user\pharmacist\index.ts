import request from '@/config/axios'
import { UserPageReqVO } from '../doctor/types'

export interface PharmacistVO {
  id: number
  natureType: string
  username: string
  mobile: number
  name: string
  sex: number
  institution: string
  relationUserId: number
  pharmacistSealUrl: string
  idCard: string
  certificateUrl: string
  isAuth: number
}

export interface PharmacistPageReqVO extends PageParam {
  name?: string
}

export interface PharmacistExcelReqVO {
  name?: string
}

export const getPharmacistOldPageApi = async (params: PharmacistPageReqVO) => {
  return await request.get({ url: '/doctor/pharmacist/old-page', params })
}

// 查询药师信息列表
export const getPharmacistPageApi = async (params: PharmacistPageReqVO) => {
  return await request.get({ url: '/doctor/pharmacist/page', params })
}

// 查询系统角色的药师信息列表
export const getPharmacistUserPageApi = async (params: UserPageReqVO) => {
  return await request.get({ url: '/system/user/page-all-pharmacist', params })
}

// 根据用户的Ids查询药师信息列表
export const getPharmacistByUserIdsApi = async (ids: Array<number>) => {
  return await request.get({ url: '/doctor/pharmacist/find-list-by-user-ids?ids=' + ids })
}

// 根据药师的Ids查询药师信息列表
export const getPharmacistByIdsApi = async (ids: Array<number>) => {
  return await request.get({ url: '/doctor/pharmacist/list?ids=' + ids })
}

// 查询药师信息详情
export const getPharmacistApi = async (id: number) => {
  return await request.get({ url: '/doctor/pharmacist/get?id=' + id })
}

// 新增药师信息
export const createPharmacistApi = async (data: PharmacistVO) => {
  return await request.post({ url: '/doctor/pharmacist/create', data })
}

// 修改药师信息
export const updatePharmacistApi = async (data: PharmacistVO) => {
  return await request.put({ url: '/doctor/pharmacist/update', data })
}

// 删除药师信息
export const deletePharmacistApi = async (id: number) => {
  return await request.delete({ url: '/doctor/pharmacist/delete?id=' + id })
}

// 导出药师信息 Excel
export const exportPharmacistApi = async (params: PharmacistExcelReqVO) => {
  return await request.download({ url: '/doctor/pharmacist/export-excel', params })
}

// 药师实名认证
export const authPharmacist = async (data: PharmacistVO) => {
  return await request.put({ url: '/doctor/pharmacist/auth', data })
}

// 查询药师信息详情
export const getPharmacistSignStatus = async (id: number) => {
  return await request.get({ url: '/doctor/pharmacist/get-pharmacist-sign-status?id=' + id })
}
// 查询药师签名地址
export const getPharmacistSignUrl = async (id: number) => {
  return await request.get({ url: '/doctor/pharmacist/get-pharmacist-sign?id=' + id })
}

// 创建药师扫码
export const createPharmacistSignStatus = async (id:number) => {
  return await request.post({ url: '/doctor/pharmacist/create-pharmacist-sign?id=' + id})
}
// 根据用户的Ids查询药师信息
export const getPharmacistByOtherUserIdsApi = async (ids: Array<number>) => {
  return await request.get({ url: '/doctor/pharmacist/find-list-by-ids?ids=' + ids })
}



