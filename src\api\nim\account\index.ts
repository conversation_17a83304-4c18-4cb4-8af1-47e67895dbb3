import request from '@/config/axios'

export interface AccountVO {
  id: number
  userUuid: string
  userName: string
  icon: string
  userId: number
  userType: number
  userToken: string
  imToken: string
  appkey: string
}

export interface AccountPageReqVO extends PageParam {
  userUuid?: string
  userName?: string
  icon?: string
  userId?: number
  userType?: number
  userToken?: string
  imToken?: string
  appkey?: string
  createTime?: Date[]
}

export interface AccountExcelReqVO {
  userUuid?: string
  userName?: string
  icon?: string
  userId?: number
  userType?: number
  userToken?: string
  imToken?: string
  appkey?: string
  createTime?: Date[]
}

// 查询音视频账号列表
export const getAccountPageApi = async (params: AccountPageReqVO) => {
  return await request.get({ url: '/nim/account/page', params })
}

// 查询音视频账号详情
export const getAccountApi = async (id: number) => {
  return await request.get({ url: '/nim/account/get?id=' + id })
}

// 新增音视频账号
export const createAccountApi = async (data: AccountVO) => {
  return await request.post({ url: '/nim/account/create', data })
}

// 修改音视频账号
export const updateAccountApi = async (data: AccountVO) => {
  return await request.put({ url: '/nim/account/update', data })
}

// 删除音视频账号
export const deleteAccountApi = async (id: number) => {
  return await request.delete({ url: '/nim/account/delete?id=' + id })
}

// 导出音视频账号 Excel
export const exportAccountApi = async (params: AccountExcelReqVO) => {
  return await request.download({ url: '/nim/account/export-excel', params })
}

// 获得可用账号列表信息，查看创建者
export const getAccountListApi= async ()=>{
  return await request.get({url: '/nim/account/find-select-list'});
}
