import request from '@/config/axios'

export interface AgreementVO {
  project: number
  type: number
  title: string
  content: string
  about: string
}

export interface AgreementPageReqVO extends PageParam {
  project?: number
  type?: number
  title?: string
  createTime?: Date[]
}

export interface AgreementExcelReqVO {
  project?: number
  type?: number
  title?: string
  createTime?: Date[]
}

// 查询协议配置列表
export const getAgreementPageApi = async (params: AgreementPageReqVO) => {
  return await request.get({ url: '/operate/agreement/page', params })
}

// 查询协议配置详情
export const getAgreementApi = async (id: number) => {
  return await request.get({ url: '/operate/agreement/get?id=' + id })
}

// 新增协议配置
export const createAgreementApi = async (data: AgreementVO) => {
  return await request.post({ url: '/operate/agreement/create', data })
}

// 修改协议配置
export const updateAgreementApi = async (data: AgreementVO) => {
  return await request.put({ url: '/operate/agreement/update', data })
}

// 删除协议配置
export const deleteAgreementApi = async (id: number) => {
  return await request.delete({ url: '/operate/agreement/delete?id=' + id })
}

// 导出协议配置 Excel
export const exportAgreementApi = async (params: AgreementExcelReqVO) => {
  return await request.download({ url: '/operate/agreement/export-excel', params })
}

// 获得协议列表
export const getBizContracts = async (data) => {
  return await request.post({ url: '/operate/bizcontract/list', data })
}

// 重签协议
export const reSignAgreement = async (data) => {
  return await request.post({ url: '/operate/bizcontract/reSignAgreement', data })
}
