import request from '@/config/axios'

export interface BankVO {
  id: number
  bankType: number
  bankCode: string
  doctorId: number
}

export interface BankPageReqVO extends PageParam {
  doctorId?: number
}

export interface BankExcelReqVO {
  doctorId?: number
}

// 查询医生银行卡号列表
export const getBankPageApi = async (params: BankPageReqVO) => {
  return await request.get({ url: '/doctor/bank/page', params })
}

// 查询医生银行卡号详情
export const getBankApi = async (id: number) => {
  return await request.get({ url: '/doctor/bank/get?id=' + id })
}

// 新增医生银行卡号
export const createBankApi = async (data: BankVO) => {
  return await request.post({ url: '/doctor/bank/create', data })
}

// 修改医生银行卡号
export const updateBankApi = async (data: BankVO) => {
  return await request.put({ url: '/doctor/bank/update', data })
}

// 删除医生银行卡号
export const deleteBankApi = async (id: number) => {
  return await request.delete({ url: '/doctor/bank/delete?id=' + id })
}

// 导出医生银行卡号 Excel
export const exportBankApi = async (params: BankExcelReqVO) => {
  return await request.download({ url: '/doctor/bank/export-excel', params })
}
