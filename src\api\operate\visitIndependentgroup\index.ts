import request from '@/config/axios'
import { awardOnStateEnum } from '@/common/enum'

export interface VisitIndependentGroupVO {
  id: number
  visitType: number
  awardType: number
  status: number
  settingType: number
  startTime?: number
  endTime?: number
  jobTime: string
  doctor_rate?: any
  village_doctor_rate?: any
}

export interface VisitIndependentGroupPageReqVO extends PageParam {
  visitType?: number
  awardType?: number
  status?: number
  settingType?: number
  startTime?: number[]
  endTime?: number[]
  jobTime?: string
  createTime?: Date[]
}

export interface VisitIndependentGroupExcelReqVO {
  visitType?: number
  awardType?: number
  status?: number
  settingType?: number
  startTime?: number[]
  endTime?: number[]
  jobTime?: string
  createTime?: Date[]
}

export interface TaskStateVO {
  id: number
  state: awardOnStateEnum
  notifyTitle: string
  notifyContent: string
}

// 查询问诊分账分组列表
export const getVisitIndependentGroupPageApi = async (params: VisitIndependentGroupPageReqVO) => {
  return await request.get({ url: '/operate/visit-independent-group/page', params })
}

// 查询问诊分账分组详情
export const getVisitIndependentGroupApi = async (id: number) => {
  return await request.get({ url: '/operate/visit-independent-group/get?id=' + id })
}

// 新增问诊分账分组
export const createVisitIndependentGroupApi = async (data: VisitIndependentGroupVO) => {
  return await request.post({ url: '/operate/visit-independent-group/create', data })
}

// 修改问诊分账分组
export const updateVisitIndependentGroupApi = async (data: VisitIndependentGroupVO) => {
  return await request.put({ url: '/operate/visit-independent-group/update', data })
}

// 删除问诊分账分组
export const deleteVisitIndependentGroupApi = async (id: number) => {
  return await request.delete({ url: '/operate/visit-independent-group/delete-visit?id=' + id })
}

// 导出问诊分账分组 Excel
export const exportVisitIndependentGroupApi = async (params: VisitIndependentGroupExcelReqVO) => {
  return await request.download({ url: '/operate/visit-independent-group/export-excel', params })
}

export const getVisitIndependentListGroupApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/visit-independent-group/list?ids=' + ids })
}

// 获得药品分账列表
export const getVisitIndependentByVisitGroupApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/visit-independent-group/list-by-visit?ids=' + ids })
}

// 新增问诊分账
export const createNewVisitIndependentGroupApi = async (data: VisitIndependentGroupVO) => {
  return await request.post({ url: '/operate/visit-independent-group/create-visit', data })
}

// 修改问诊分账分组
export const updateNewVisitIndependentGroupApi = async (data: VisitIndependentGroupVO) => {
  return await request.put({ url: '/operate/visit-independent-group/update-visit', data })
}

//修改启用状态
export const TaskStateDrugProfitGroupApi = async (data: TaskStateVO) => {
  return await request.post({ url: '/operate/visit-independent-group/task-on-state', data })
}
