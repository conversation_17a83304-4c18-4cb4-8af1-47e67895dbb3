import request from '@/config/axios'

export interface EmrVO {
  id: number
  diagnoseId: number
  diagnoseType: number
  patientId: number
  sickId: number
  doctorId: number
  name: string
  sex: number
  age: number
  diseaseDescription: string
  initialDiagnosis: string
  content: string
  principleAction: string
  currentIllness: string
  personIllness: string
  imgType: number
}

export interface EmrPageReqVO extends PageParam {
  diagnoseId?: number
  diagnoseType?: number
  sickId?: number
}

export interface EmrExcelReqVO {
  diagnoseId?: number
  diagnoseType?: number
  sickId?: number
}

// 查询电子病历列表
export const getEmrPageApi = async (params: EmrPageReqVO) => {
  return await request.get({ url: '/patient/emr/page', params })
}

// 查询电子病历详情
export const getEmrApi = async (id: number) => {
  return await request.get({ url: '/patient/emr/get?id=' + id })
}

// 新增电子病历
export const createEmrApi = async (data: EmrVO) => {
  return await request.post({ url: '/patient/emr/create', data })
}

// 修改电子病历
export const updateEmrApi = async (data: EmrVO) => {
  return await request.put({ url: '/patient/emr/update', data })
}

// 删除电子病历
export const deleteEmrApi = async (id: number) => {
  return await request.delete({ url: '/patient/emr/delete?id=' + id })
}

// 导出电子病历 Excel
export const exportEmrApi = async (params: EmrExcelReqVO) => {
  return await request.download({ url: '/patient/emr/export-excel', params })
}


// 查询电子病历详情
export const getEmrByVisitIdsApi = async (visitIds: Array<number>) => {
  return await request.get({ url: '/patient/emr/find-emr-list-by-visitIds?visitIds=' + visitIds })
}


