import service from '../https'
import { ErrorModel, SuccessModel } from '@/common/model/resModel'
import { completionInfoModel } from '@/common/model/im/patient'

/** 获取问诊订单完成情况
 * @param {string} sid sessionId
 */
export const getOrderInfoBySid = (sid: string) => {
  return service
    .get<completionInfoModel>('/app-api/doctor/visit-api/find-visit-status-by-sid', {
      data: {
        sid
      }
    })
    .then((response) => {
      return response as SuccessModel<completionInfoModel>
    })
    .catch(() => {
      return new ErrorModel({ data: '' })
    })
}
