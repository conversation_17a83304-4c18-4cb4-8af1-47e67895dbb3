import request from '@/config/axios'

export interface PrescriptionDrugVO {
}

export interface PrescriptionDrugPageReqVO extends PageParam {
  name?: string
  drugOrderId?: number
  rxId?: number
}

export interface PrescriptionDrugExcelReqVO {
  name?: string
  drugOrderId?: number
  rxId?: number
}

// 查询患者处方药品信息列表
export const getPrescriptionDrugPageApi = async (params: PrescriptionDrugPageReqVO) => {
  return await request.get({ url: '/patient/prescription-drug/page', params })
}

// 查询患者处方药品信息详情
export const getPrescriptionDrugApi = async (id: number) => {
  return await request.get({ url: '/patient/prescription-drug/get?id=' + id })
}

// 新增患者处方药品信息
export const createPrescriptionDrugApi = async (data: PrescriptionDrugVO) => {
  return await request.post({ url: '/patient/prescription-drug/create', data })
}

// 修改患者处方药品信息
export const updatePrescriptionDrugApi = async (data: PrescriptionDrugVO) => {
  return await request.put({ url: '/patient/prescription-drug/update', data })
}

// 删除患者处方药品信息
export const deletePrescriptionDrugApi = async (id: number) => {
  return await request.delete({ url: '/patient/prescription-drug/delete?id=' + id })
}

// 导出患者处方药品信息 Excel
export const exportPrescriptionDrugApi = async (params: PrescriptionDrugExcelReqVO) => {
  return await request.download({ url: '/patient/prescription-drug/export-excel', params })
}
