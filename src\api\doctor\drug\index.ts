import request from '@/config/axios'

export interface DrugVO {
  id: number
  doctorId: number
  drugId: number
  drugTypeId: number
}

export interface DrugPageReqVO extends PageParam {
  doctorId?: number
}

export interface DrugExcelReqVO {
  doctorId?: number
}

// 查询医生药库信息列表
export const getDrugPageApi = async (params: DrugPageReqVO) => {
  return await request.get({ url: '/doctor/drug/page', params })
}

// 查询医生药库信息详情
export const getDrugApi = async (id: number) => {
  return await request.get({ url: '/doctor/drug/get?id=' + id })
}

// 根据集合查询医生药库信息详情
export const getDrugListApi = async (ids: number[]) => {
  return await request.get({ url: '/doctor/drug/list?ids='+ids})
}

// 新增医生药库信息
export const createDrugApi = async (data: DrugVO) => {
  return await request.post({ url: '/doctor/drug/create', data })
}

// 修改医生药库信息
export const updateDrugApi = async (data: DrugVO) => {
  return await request.put({ url: '/doctor/drug/update', data })
}

// 删除医生药库信息
export const deleteDrugApi = async (id: number) => {
  return await request.delete({ url: '/doctor/drug/delete?id=' + id })
}

// 导出医生药库信息 Excel
export const exportDrugApi = async (params: DrugExcelReqVO) => {
  return await request.download({ url: '/doctor/drug/export-excel', params })
}
