import request from '@/config/axios'

// 获得医生灵工信息分页
export const getDoctorFlexibleEmployeePage = async (data) => {
  return await request.post({ url: '/doctor/user/flexible-employee-page', data })
}

// 同步到灵工
export const syncDoctorFlexibleEmployee = async (data) => {
  return await request.post({ url: '/doctor/user/sync-flexibleemployee', data })
}

// 批量同步到灵工
export const batchSyncDoctorFlexibleEmployee = async (data) => {
  return await request.post({ url: '/doctor/user/sync-batch-flexible-employee', data })
}

// 获得村医灵工信息分页
export const getGpFlexibleEmployeePage = async (data) => {
  return await request.post({ url: '/gp/user/flexible-employee-page', data })
}

// 同步到灵工
export const syncGpFlexibleEmployee = async (data) => {
  return await request.post({ url: '/gp/user/sync-flexibleemployee', data })
}

// 批量同步到灵工
export const batchSyncGpFlexibleEmployee = async (data) => {
  return await request.post({ url: '/gp/user/sync-batch-flexible-employee', data })
}
