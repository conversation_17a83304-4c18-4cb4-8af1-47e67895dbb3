import { awardOnStateEnum } from '@/common/enum'
import request from '@/config/axios'

export interface DrugProfitGroupVO {
  id: number
  drugId: number
  awardType: number
  status: number
  startTime: number
  endTime: number
  jobTime: string
  onState: number
  doctor_rate?: any
  village_doctor_rate?: any
}

export interface DrugProfitGroupPageReqVO extends PageParam {
  drugId?: number
  awardType?: number
  status?: number
  startTime?: number[]
  endTime?: number[]
  jobTime?: string
  createTime?: Date[]
}

export interface DrugProfitGroupExcelReqVO {
  drugId?: number
  awardType?: number
  status?: number
  startTime?: number[]
  endTime?: number[]
  jobTime?: string
  createTime?: Date[]
}

export interface TaskStateVO {
  id: number
  state: awardOnStateEnum
  notifyTitle: string
  notifyContent: string
}

// 查询药品分润分组列表
export const getDrugProfitGroupPageApi = async (params: DrugProfitGroupPageReqVO) => {
  return await request.get({ url: '/operate/drug-profit-group/page', params })
}

// 获得药品分润列表
export const getDrugProfitGroupListApi = async (ids: number[]) => {
  return await request.get({ url: '/operate/drug-profit-group/list?ids=' + ids })
}

// 查询药品分润分组详情
export const getDrugProfitGroupApi = async (id: number) => {
  return await request.get({ url: '/operate/drug-profit-group/get?id=' + id })
}

// 新增药品分润分组
export const createDrugProfitGroupApi = async (data: DrugProfitGroupVO) => {
  return await request.post({ url: '/operate/drug-profit-group/create', data })
}

// 修改药品分润分组
export const updateDrugProfitGroupApi = async (data: DrugProfitGroupVO) => {
  return await request.put({ url: '/operate/drug-profit-group/update', data })
}

// 删除药品分润分组
export const deleteDrugProfitGroupApi = async (id: number) => {
  return await request.delete({ url: '/operate/drug-profit-group/delete?id=' + id })
}

// 导出药品分润分组 Excel
export const exportDrugProfitGroupApi = async (params: DrugProfitGroupExcelReqVO) => {
  return await request.download({ url: '/operate/drug-profit-group/export-excel', params })
}

//修改启用状态
export const TaskStateDrugProfitGroupApi = async (data: TaskStateVO) => {
  return await request.post({ url: '/operate/drug-profit-group/task-on-state', data })
}
