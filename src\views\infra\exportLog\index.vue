<template>
  <ContentWrap>
    <!-- 列表 -->
    <XTable @register="registerTable">
      <!-- 操作：导出 -->
      <template #toolbar_buttons>
        <XButton
          type="warning"
          preIcon="ep:download"
          :title="t('action.export')"
          v-hasPermi="['infra:export-log:export']"
          @click="exportList('数据导出日志.xls')"
        />
      </template>
      
      <!-- 状态显示 -->
      <template #status_default="{ row }">
        <el-tag :type="row.status ? 'success' : 'warning'">
          {{ row.status ? '已完成' : '进行中' }}
        </el-tag>
      </template>
      
      <!-- 操作按钮 -->
      <template #actionbtns_default="{ row }">
        <!-- 操作：详情 -->
        <XTextButton
          preIcon="ep:view"
          :title="t('action.detail')"
          v-hasPermi="['infra:export-log:query']"
          @click="handleDetail(row)"
        />
        <!-- 操作：下载 -->
        <XTextButton
          preIcon="ep:download"
          title="下载文件"
          v-if="row.status && row.fileUrl"
          v-hasPermi="['infra:export-log:download']"
          @click="handleDownload(row)"
        />
      </template>
    </XTable>
  </ContentWrap>
  
  <!-- 详情弹窗 -->
  <XModal v-model="dialogVisible" :title="dialogTitle" width="800px">
    <!-- 对话框(详情) -->
    <Descriptions :schema="allSchemas.detailSchema" :data="detailData">
      <template #status="{ row }">
        <el-tag :type="row.status ? 'success' : 'warning'">
          {{ row.status ? '已完成' : '进行中' }}
        </el-tag>
      </template>
      <template #fileUrl="{ row }">
        <el-link 
          v-if="row.status && row.fileUrl" 
          type="primary" 
          @click="handleDownload(row)"
        >
          点击下载
        </el-link>
        <span v-else>--</span>
      </template>
    </Descriptions>
    <!-- 操作按钮 -->
    <template #footer>
      <XButton :title="t('dialog.close')" @click="dialogVisible = false" />
    </template>
  </XModal>
</template>

<script setup lang="ts" name="ExportLog">
import { allSchemas } from './exportLog.data'
import * as ExportLogApi from '@/api/infra/exportLog'

const { t } = useI18n() // 国际化
const message = useMessage()

// ========== 列表相关 ==========
const [registerTable, { exportList }] = useXTable({
  allSchemas: allSchemas,
  getListApi: ExportLogApi.getExportLogPageApi,
  exportListApi: ExportLogApi.exportExportLogApi
})

// ========== 详情相关 ==========
const detailData = ref() // 详情 Ref
const dialogVisible = ref(false) // 是否显示弹出层
const dialogTitle = ref('') // 弹出层标题

// 详情操作
const handleDetail = async (row: ExportLogApi.ExportLogVO) => {
  // 设置数据
  detailData.value = row
  dialogTitle.value = t('action.detail')
  dialogVisible.value = true
}

// 下载文件操作
const handleDownload = async (row: ExportLogApi.ExportLogVO) => {
  if (!row.fileUrl) {
    message.error('文件地址不存在')
    return
  }
  
  try {
    await ExportLogApi.downloadExportFileApi(row.fileUrl)
    message.success('文件下载成功')
  } catch (error) {
    console.error('下载失败:', error)
    message.error('文件下载失败')
  }
}
</script>
