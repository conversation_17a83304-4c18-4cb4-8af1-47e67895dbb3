<template>
  <ContentWrap>
    <!-- 列表 -->
    <XTable @register="registerTable">
      <!-- 操作：导出 -->
      <template #toolbar_buttons>
        <XButton
          type="warning"
          preIcon="ep:download"
          :title="t('action.export')"
          v-hasPermi="['infra:export-log:export']"
          @click="exportList('数据导出日志.xls')"
        />
      </template>
      
      <!-- 状态显示 -->
      <template #status_default="{ row }">
        <el-tag :type="row.status ? 'success' : 'warning'">
          {{ row.status ? '已完成' : '进行中' }}
        </el-tag>
      </template>
      
      <!-- 操作按钮 -->
      <template #actionbtns_default="{ row }">
        <!-- 操作：下载 -->
        <XTextButton
          preIcon="ep:download"
          title="下载文件"
          v-if="row.status && row.fileUrl"
          @click="handleDownload(row)"
        />
      </template>
    </XTable>
  </ContentWrap>
  
  <!-- 详情弹窗 -->
  <XModal v-model="dialogVisible" :title="dialogTitle" width="800px">
    <!-- 对话框(详情) -->
    <Descriptions :schema="allSchemas.detailSchema" :data="detailData">
      <template #status="{ row }">
        <el-tag :type="row.status ? 'success' : 'warning'">
          {{ row.status ? '已完成' : '进行中' }}
        </el-tag>
      </template>
      <template #fileUrl="{ row }">
        <el-link 
          v-if="row.status && row.fileUrl" 
          type="primary" 
          @click="handleDownload(row)"
        >
          点击下载
        </el-link>
        <span v-else>--</span>
      </template>
    </Descriptions>
    <!-- 操作按钮 -->
    <template #footer>
      <XButton :title="t('dialog.close')" @click="dialogVisible = false" />
    </template>
  </XModal>
</template>

<script setup lang="ts" name="ExportLog">
import { allSchemas } from './exportLog.data'
import * as ExportLogApi from '@/api/infra/exportLog'

const { t } = useI18n() // 国际化
const message = useMessage()

// ========== 列表相关 ==========
const [registerTable, { exportList }] = useXTable({
  allSchemas: allSchemas,
  getListApi: ExportLogApi.getExportLogPageApi,
  exportListApi: ExportLogApi.exportExportLogApi
})

// ========== 详情相关 ==========
const detailData = ref() // 详情 Ref
const dialogVisible = ref(false) // 是否显示弹出层
const dialogTitle = ref('') // 弹出层标题

// 下载文件操作
const handleDownload = async (row: ExportLogApi.ExportLogVO) => {
  if (!row.fileUrl) {
    message.error('文件地址不存在')
    return
  }
  
  try {
    // 使用 row.type 作为文件名，如果没有则使用默认名称
    const fileName = row.type || '导出文件'
    
    try {
      // 方法1: 通过fetch获取文件并创建blob来确保文件名设置生效
      const response = await fetch(row.fileUrl)
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`)
      }
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = fileName
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // 清理blob URL
      window.URL.revokeObjectURL(url)
    } catch (fetchError) {
      // 方法2: 如果fetch失败，回退到直接下载方式
      console.warn('Fetch下载失败，回退到直接下载:', fetchError)
      const link = document.createElement('a')
      link.href = row.fileUrl
      link.download = fileName
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  } catch (error: any) {
    message.error('文件下载失败，请检查文件地址是否正确')
  }
}
</script>
