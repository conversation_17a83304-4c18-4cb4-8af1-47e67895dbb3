<template>
  <ContentWrap>
    <!-- 列表 -->
    <vxe-grid ref="xGrid" v-bind="gridOptions" class="xtable-scrollbar">
      <template #actionbtns_default="{ row }">
        <!-- 操作：修改 -->
        <XTextButton
          v-show="row.isFollowUp === 0"
          title="发起随访"
          v-hasPermi="['patient:follow-visit:create']"
          @click="handleCreateFollowVisit(row.id)"
        />

        <XTextButton
            title="结束问诊"
            v-if="row.status === 2"
            v-hasPermi="['visit:inquiry:end']"
            @click="handleFinishVisit(row.sid)"
        />


        <!-- 操作：详情 -->
        <XTextButton
          preIcon="ep:view"
          :title="t('action.detail')"
          v-hasPermi="['visit:inquiry:detail']"
          @click="handleDetail(row)"
        />

        <!-- 操作：问诊详情 -->
        <XTextButton
          preIcon="ep:view"
          title="问诊详情"
          v-hasPermi="['visit:inquiry:visit-detail']"
          @click="handleVisitDetail(row)"
        />
        <XTextButton
          preIcon="ep:view"
          title="患者详情"
          v-hasPermi="['visit:inquiry:sick-detail']"
          @click="handleSickDetail(row)"
        />
        <!-- 操作：删除 -->
        <XTextButton
          preIcon="ep:delete"
          :title="t('action.del')"
          v-hasPermi="['patient:visit:delete']"
          @click="handleDelete(row.id)"
        />
      </template>
    </vxe-grid>
  </ContentWrap>
  <!-- 弹窗 -->
  <XModal id="visitModel" :loading="modelLoading" v-model="modelVisible" :title="modelTitle" >
    <!-- 表单：添加/修改 -->
    <Form
      ref="formRef"
      v-if="['发起随访'].includes(actionType)"
      :schema="allSchemas?.formSchema"
      :rules="rules"
      :isCol="false"
    />
    <!-- 表单：详情 -->
    <Descriptions
      v-if="actionType === 'detail'"
      :schema="allSchemas?.detailSchema"
      :data="detailData"
    />

    <MenuTab
      v-if="actionType === 'sickDetail'"
      :sickId="detailSickId"
      :default-descriptions="{
        schema: userSick.allSchemas.detailSchema,
        data: detailData
      }"
    />

    <VisitDetail v-if="actionType === 'visitdetail'" :data="visitDetailData" />

    <template #footer>
      <!-- 按钮：保存 -->
      <XButton
        v-if="['发起随访'].includes(actionType)"
        type="primary"
        :title="t('action.save')"
        :loading="actionLoading"
        @click="submitForm()"
      />
      <!-- 按钮：关闭 -->
      <XButton :loading="actionLoading" :title="t('dialog.close')" @click="modelVisible = false" />
    </template>
  </XModal>
</template>
<script setup lang="ts" name="Visit">
// 全局相关的 import
import { ref, unref } from 'vue'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { useVxeGrid } from '@/hooks/web/useVxeGrid'
import { VxeGridInstance } from 'vxe-table'
import { FormExpose } from '@/components/Form'
import {ElMessageBox, ElNotification} from 'element-plus'
// 业务相关的 import
import { rules, allSchemas } from './visit.data'
import * as VisitApi from '@/api/visit/inquiry'
import { createFollowVisitApi, FollowVisitVO } from '@/api/visit/followvisit'
import { getVisitPageListApi } from '.'
// @ts-ignore
import VisitDetail from './components/visitdetail/visitdetail.vue'
import { ageTrnslate } from '@/utils/ageTranslate'
import { useUserStore } from '@/store/modules/user'
import { MenuTab } from '@/views/user/userSick/components/index'
import * as userSick from '@/views/user/userSick/userSick.data'
import { getUserSickApi } from '@/api/user/userSick'
const userStore = useUserStore()

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const sss: any = ref({})

sss.value = allSchemas

// 列表相关的变量
const xGrid = ref<VxeGridInstance>() // 列表 Grid Ref
const { gridOptions, getList, deleteData } = useVxeGrid<VisitApi.VisitVO>({
  allSchemas: allSchemas,
  getListApi: getVisitPageListApi,
  deleteApi: VisitApi.deleteVisitApi,
  exportListApi: VisitApi.exportVisitApi
})

// 弹窗相关的变量
const modelVisible = ref(false) // 是否显示弹出层
const modelTitle = ref('edit') // 弹出层标题
const modelLoading = ref(false) // 弹出层loading
const actionType = ref('') // 操作按钮的类型
const actionLoading = ref(false) // 按钮 Loading
const formRef = ref<FormExpose>() // 表单 Ref
const detailData = ref() // 详情 Ref
const visitDetailData = ref<any>({}) //问诊详情 Ref
const detailSickId = ref() // 患者id

// 设置标题
const setDialogTile = (type: string) => {
  modelLoading.value = true
  modelTitle.value = t('action.' + type)
  actionType.value = type
  modelVisible.value = true
}

// // 修改操作
// const handleUpdate = async (rowId: number) => {
//   setDialogTile('update')
//   // 设置数据
//   const res = await VisitApi.getVisitApi(rowId)
//   unref(formRef)?.setValues(res)
//   modelLoading.value = false
// }

// 修改操作
const handleFinishVisit = async (sid: string) => {
  try {
    await ElMessageBox.confirm('确定要结束该问诊？', '结束问诊')
    const res = await VisitApi.finishVisitApi(sid)
    if (!!res) {
      ElNotification.success('结束问诊成功！')
      nextTick(() => getList(xGrid))
    } else {
      ElNotification.error('结束问诊失败！请重新操作！')
    }
  } catch (error) {
    await nextTick()
  }
}


const handleCreateFollowVisit = async (rowId: number) => {
  setDialogTile('发起随访')
  modelTitle.value  = '发起随访'
  const user = await userStore.getUser
  // 设置数据
  const res = await VisitApi.getVisitApi(rowId)
  const data = {
    followDoctorType: 4,
    followDoctorId: res.doctorId,
    sickId: res.sickId,
    visitType: res.visitType,
    visitId: res.id,
    visitTime: null,
    therapeuticEffect: null,
    compliance: null,
    adverseReaction: null,
    content: '',
    annexList: [],
    creatorId: user.id,
  }
  
  unref(formRef)?.setValues(data)
  modelLoading.value = false
}

// 详情操作
const handleDetail = async (row: any) => {
  setDialogTile('detail')
  const res = await VisitApi.getVisitApi(row.id)
  detailData.value = res

  detailData.value.doctorName = row.doctorName

  detailData.value.sickName = row.sickName
  detailData.value.sickPhone = row.sickPhone
  detailData.value.isPrescription = row.isPrescription
  detailData.value.sickAge = ageTrnslate(detailData.value.sickAge)

  detailData.value.sickAge = row.sickAge

  modelLoading.value = false
}
const handleVisitDetail = async (row: any) => {
  try {
    // 使用V2接口获取详细数据
    const res = await VisitApi.getVisitV2Api(row.id)
    const data = res

    // 基础信息映射
    visitDetailData.value.visitId = data.id
    visitDetailData.value.orderNum = row.orderNum // 从列表数据获取
    visitDetailData.value.createTime = data.visitOrder?.createTime || row.createTime
    visitDetailData.value.price = data.visitOrder?.amount || row.price
    visitDetailData.value.status = data.visitOrder?.visitStatus || row.status
    visitDetailData.value.visitType = row.visitType
    visitDetailData.value.visitSid = row.sid

    // 患者信息映射
    if (data.sickInfo) {
      visitDetailData.value.sickName = data.sickInfo.sickName
      visitDetailData.value.sickPhone = data.sickInfo.sickPhone
      visitDetailData.value.sickAge = data.sickInfo.age
      visitDetailData.value.sickBirthday = data.sickInfo.birthday
      visitDetailData.value.sickContent = data.sickInfo.diseaseContent
      visitDetailData.value.sickSex = data.sickInfo.sickSex === 1 ? '男' : '女'
      visitDetailData.value.sickIdCard = '' // V2接口中没有此字段
    }

    // 医生信息映射
    if (data.doctorInfo) {
      visitDetailData.value.doctorId = data.doctorInfo.doctorId
      visitDetailData.value.doctorHeadImg = data.doctorInfo.headImg
      visitDetailData.value.doctorName = data.doctorInfo.doctorName
      visitDetailData.value.officeHolderName = data.doctorInfo.officeHolderName
      visitDetailData.value.doctorHospitalDepart = data.doctorInfo.hospitalDepart
      visitDetailData.value.doctorHospitalName = data.doctorInfo.hospitalName
    }

    // 业务数据映射
    visitDetailData.value.visitOrder = data.visitOrder
    visitDetailData.value.prescriptions = data.prescriptions
    visitDetailData.value.emr = data.emrRespVo
    visitDetailData.value.evaluate = data.evaluateInfo

    // 媒体数据映射
    visitDetailData.value.sickCheckDataImages = data.emrRespVo?.visitImgUrl || []
    visitDetailData.value.doctorCheckDataImages = data.emrRespVo?.assistImgUrl || []

    setDialogTile('visitdetail')
    modelTitle.value = '问诊详情'
    modelLoading.value = false
  } catch (error) {
    console.error('获取问诊详情失败:', error)
    ElNotification.error('获取问诊详情失败')
    modelLoading.value = false
  }
}

// 删除操作
const handleDelete = async (rowId: number) => {
  await deleteData(xGrid, rowId)
}

// 提交按钮
const submitForm = async () => {
  const elForm = unref(formRef)?.getElFormRef()
  if (!elForm) return
  elForm.validate(async (valid) => {
    if (valid) {
      actionLoading.value = true
      // 提交请求
      try {
        const data = JSON.parse(JSON.stringify(unref(formRef)?.formModel as FollowVisitVO))
        data.annexList = data.annexList.map(v => {
          return v.url
        })
        await createFollowVisitApi(data)
        message.success('发起成功')
        modelVisible.value = false
      } finally {
        actionLoading.value = false
        // 刷新列表
        await getList(xGrid)
      }
    }
  })
}

const handleSickDetail = async(row: any) => {
  detailSickId.value = row.sickId
  const res = await getUserSickApi(detailSickId.value)
  detailData.value = res
  setDialogTile('sickDetail')
  modelTitle.value = '患者详情'
  modelLoading.value = false
}
</script>
