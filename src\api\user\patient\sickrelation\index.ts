import request from '@/config/axios'

export interface SickRelationVO {
  id: number
  patientId: number
  sickId: number
  relationType: number
  isDefault: number
  status: number
  bindTime: Date
}

export interface SickRelationPageReqVO extends PageParam {
  patientId?: number
  sickId?: number
  relationType?: number
  isDefault?: number
  status?: number
  bindTime?: Date[]
  createTime?: Date[]
}

export interface SickRelationExcelReqVO {
  patientId?: number
  sickId?: number
  relationType?: number
  isDefault?: number
  status?: number
  bindTime?: Date[]
  createTime?: Date[]
}

// 查询患者账号绑定关系列表
export const getSickRelationPageApi = async (params: SickRelationPageReqVO) => {
  return await request.get({ url: '/patient/sick-relation/page', params })
}

// 查询患者账号绑定关系详情
export const getSickRelationApi = async (id: number) => {
  return await request.get({ url: '/patient/sick-relation/get?id=' + id })
}

// 新增患者账号绑定关系
export const createSickRelationApi = async (data: SickRelationVO) => {
  return await request.post({ url: '/patient/sick-relation/create', data })
}

// 修改患者账号绑定关系
export const updateSickRelationApi = async (data: SickRelationVO) => {
  return await request.put({ url: '/patient/sick-relation/update', data })
}

// 删除患者账号绑定关系
export const deleteSickRelationApi = async (id: number) => {
  return await request.delete({ url: '/patient/sick-relation/delete?id=' + id })
}

// 导出患者账号绑定关系 Excel
export const exportSickRelationApi = async (params: SickRelationExcelReqVO) => {
  return await request.download({ url: '/patient/sick-relation/export-excel', params })
}


// 查询村医患者绑定关系列表
export const getSickRelationByPatientListApi = async (patientId: number) => {
  return await request.get({ url: '/patient/sick-relation/list-by-patient', params: { id: patientId } })
}
